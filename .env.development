# Development Environment Configuration for MaBourse Frontend
# This file contains development-specific environment variables

# API Configuration
REACT_APP_API_URL=http://localhost:5000
REACT_APP_USE_REAL_API=true

# Application Configuration
REACT_APP_APP_NAME=MaBourse - Development
REACT_APP_DEFAULT_LANGUAGE=fr
REACT_APP_DEBUG_MODE=true

# Feature Flags
REACT_APP_ENABLE_TWO_FACTOR=false
REACT_APP_ENABLE_EMAIL_NOTIFICATIONS=false
REACT_APP_ENABLE_ANALYTICS=false

# Security Configuration
REACT_APP_ENABLE_HTTPS_ONLY=false
REACT_APP_ENABLE_STRICT_CSP=false

# Performance Configuration
REACT_APP_ENABLE_SERVICE_WORKER=false
REACT_APP_ENABLE_CODE_SPLITTING=true

# Build Configuration
GENERATE_SOURCEMAP=true
REACT_APP_VERSION=$npm_package_version

# Development Tools
REACT_APP_ENABLE_REDUX_DEVTOOLS=true
REACT_APP_ENABLE_REACT_DEVTOOLS=true
