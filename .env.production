# Production Environment Configuration for MaBourse Frontend
# This file contains production-ready environment variables

# API Configuration
REACT_APP_API_URL=https://api.mabourse.com
REACT_APP_USE_REAL_API=true

# Application Configuration
REACT_APP_APP_NAME=MaBourse - Scholarship Portal
REACT_APP_DEFAULT_LANGUAGE=fr
REACT_APP_DEBUG_MODE=false

# Feature Flags
REACT_APP_ENABLE_TWO_FACTOR=true
REACT_APP_ENABLE_EMAIL_NOTIFICATIONS=true
REACT_APP_ENABLE_ANALYTICS=true

# Security Configuration
REACT_APP_ENABLE_HTTPS_ONLY=true
REACT_APP_ENABLE_STRICT_CSP=true

# Performance Configuration
REACT_APP_ENABLE_SERVICE_WORKER=true
REACT_APP_ENABLE_CODE_SPLITTING=true

# Build Configuration
GENERATE_SOURCEMAP=false
REACT_APP_VERSION=$npm_package_version
