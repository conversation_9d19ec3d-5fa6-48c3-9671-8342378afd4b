PORT=5000
# PRODUCTION-GRADE JWT SECURITY
JWT_SECRET=MaBourse2025$SecureJWT#Key!Development@Only&Random*String%ForMaxSecurity789
JWT_EXPIRATION=2h
REFRESH_TOKEN_SECRET=MaBourse2025$RefreshToken#Key!Dev@Only&Rotation*String%ForSecurity456
REFRESH_TOKEN_EXPIRATION=7d
JWT_ISSUER=mabourse-admin-portal
JWT_AUDIENCE=mabourse-admin-users
JWT_ALGORITHM=HS256
NODE_ENV=development

# Email configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=MaBourse <<EMAIL>>

# Database configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=mabourse

# Database URL for PostgreSQL
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/mabourse

# Admin configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_NAME=Main Administrator

# CORS configuration
CORS_ORIGIN=http://localhost:3000

# Frontend URL for password reset links
FRONTEND_URL=http://localhost:3000

# Logging
LOG_LEVEL=debug

# Rate limiting (temporarily increased for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
