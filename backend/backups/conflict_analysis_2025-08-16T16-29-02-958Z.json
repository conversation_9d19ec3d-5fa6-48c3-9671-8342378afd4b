{"prismaFiles": ["/Users/<USER>/Documents/GitHub/maboursewebsite/backend/scripts/seed-test-data.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/controllers/admin.controller.fixed.test.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/controllers/admin.controller.test.js", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/controllers/scholarship.controller.fixed.test.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/controllers/scholarship.controller.test.js", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/controllers/scholarship.controller.test.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/controllers/user.controller.fixed.test.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/__tests__/helpers/test-utils.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/security.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/cleanupDuplicateData.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/createNewAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/database-conflict-analyzer.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/fixAdminPrivileges.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/fixNonMainAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/seedScholarships.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/updateControllers.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/cleanupUtils.ts"], "sequelizeFiles": ["/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/index.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/checkDatabase.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/cleanupDuplicateAdmins.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/cleanupDuplicateData.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/completeMigration.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/createSequelizeAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/database-conflict-analyzer.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/removeSequelize.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/updateControllers.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/cleanupUtils.ts"], "rawQueryFiles": ["/Users/<USER>/Documents/GitHub/maboursewebsite/backend/reset-admin-password.js", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/scripts/create-guides-table.js", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/scripts/fix-image-paths.js", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/scripts/setupAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/config/database.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/auth.controller.new.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/country.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/deviceTrust.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/guide.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/opportunity.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/scholarship.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/controllers/security.dashboard.controller.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/database/migrate.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/database/run-migration.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/middleware/adaptiveRateLimit.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/middleware/requestSigning.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/Admin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/ContactSettings.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/Guide.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/Message.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/Newsletter.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/Opportunity.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/Scholarship.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/models/User.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/routes/admin.routes.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/routes/deviceTrust.routes.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/routes/security.csp.routes.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/routes/security.dashboard.routes.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/addColumns.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/addNewTables.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/addPasswordSecurityColumns.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/addRandomThumbnails.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/addSampleOpportunities.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/checkDatabaseHealth.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/database-conflict-analyzer.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/emergency-backup.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/fixDatabaseSchema.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/initializePasswordHistory.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/migrateAPISecurityFeatures.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/migrateBehavioralPatterns.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/migrateCSPFeatures.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/migrateDeviceApproval.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/migrateSecurityTables.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/secureAdminSetup.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/validateSecurity.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/services/passwordResetService.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/services/sessionManager.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/migrateThumbnails.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/mlAnomalyDetection.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/passwordPolicy.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/securityMonitor.ts"], "configFiles": [], "modelFiles": [], "migrationFiles": [], "duplicateControllers": [], "conflictingRoutes": ["/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/routes/scholarship.routes.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/routes/scholarships.ts"]}