{"prismaFilesRemoved": ["/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/cleanupDuplicateData.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/createNewAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/fixAdminPrivileges.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/fixNonMainAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/seedScholarships.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/utils/cleanupUtils.ts"], "sequelizeFilesRemoved": ["/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/checkDatabase.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/cleanupDuplicateAdmins.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/createSequelizeAdmin.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/removeSequelize.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/completeMigration.ts", "/Users/<USER>/Documents/GitHub/maboursewebsite/backend/src/scripts/updateControllers.ts"], "tablesDropped": ["_prisma_migrations", "Admin", "Message", "Newsletter", "PasswordHistory", "Scholarship", "SecurityEvent", "SecuritySettings", "User"], "dependenciesRemoved": [], "configsCleaned": ["tsconfig.json"], "errors": []}