"use strict";
/**
 * Guide Controller
 *
 * Handles all guide-related operations with industry-standard practices
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteGuide = exports.updateGuide = exports.createGuide = exports.searchGuides = exports.getGuidesByCategory = exports.getGuideBySlug = exports.getGuideById = exports.getAllGuides = void 0;
const Guide_1 = require("../models/Guide");
const response_util_1 = require("../utils/response.util");
const validation_util_1 = require("../utils/validation.util");
const database_1 = require("../config/database");
/**
 * Get all guides with filtering and pagination
 */
const getAllGuides = async (req, res) => {
    try {
        const { page = 1, limit = 10, category, published, orderBy = 'created_at', orderDirection = 'DESC' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        // Use direct query as fallback to ensure reliability
        let queryText = 'SELECT * FROM guides WHERE 1=1';
        const params = [];
        let paramCount = 1;
        // Add filters
        if (published === 'true') {
            queryText += ` AND is_published = $${paramCount}`;
            params.push(true);
            paramCount++;
        }
        else if (published === 'false') {
            queryText += ` AND is_published = $${paramCount}`;
            params.push(false);
            paramCount++;
        }
        if (category) {
            queryText += ` AND category = $${paramCount}`;
            params.push(category);
            paramCount++;
        }
        // Add ordering
        const validOrderBy = ['created_at', 'title', 'read_time'].includes(orderBy) ? orderBy : 'created_at';
        const validDirection = ['ASC', 'DESC'].includes(orderDirection) ? orderDirection : 'DESC';
        queryText += ` ORDER BY ${validOrderBy} ${validDirection}`;
        // Add pagination
        queryText += ` LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
        params.push(Number(limit), offset);
        const result = await (0, database_1.query)(queryText, params);
        const guides = result.rows;
        // Get total count for pagination
        let countQuery = 'SELECT COUNT(*) FROM guides WHERE 1=1';
        const countParams = [];
        let countParamCount = 1;
        if (published === 'true') {
            countQuery += ` AND is_published = $${countParamCount}`;
            countParams.push(true);
            countParamCount++;
        }
        else if (published === 'false') {
            countQuery += ` AND is_published = $${countParamCount}`;
            countParams.push(false);
            countParamCount++;
        }
        if (category) {
            countQuery += ` AND category = $${countParamCount}`;
            countParams.push(category);
        }
        const countResult = await (0, database_1.query)(countQuery, countParams);
        const total = parseInt(countResult.rows[0].count);
        (0, response_util_1.sendSuccess)(res, 'Guides retrieved successfully', guides);
    }
    catch (error) {
        console.error('Error fetching guides:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch guides', error);
    }
};
exports.getAllGuides = getAllGuides;
/**
 * Get guide by ID
 */
const getGuideById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id || isNaN(Number(id))) {
            (0, response_util_1.sendError)(res, 'Invalid guide ID', null, 400);
            return;
        }
        const guide = await Guide_1.Guide.findById(Number(id));
        if (!guide) {
            (0, response_util_1.sendError)(res, 'Guide not found', null, 404);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Guide retrieved successfully', guide);
    }
    catch (error) {
        console.error('Error fetching guide:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch guide', error);
    }
};
exports.getGuideById = getGuideById;
/**
 * Get guide by slug
 */
const getGuideBySlug = async (req, res) => {
    try {
        const { slug } = req.params;
        if (!slug) {
            (0, response_util_1.sendError)(res, 'Slug is required', null, 400);
            return;
        }
        const guide = await Guide_1.Guide.findBySlug(slug);
        if (!guide) {
            (0, response_util_1.sendError)(res, 'Guide not found', null, 404);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Guide retrieved successfully', guide);
    }
    catch (error) {
        console.error('Error fetching guide by slug:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch guide', error);
    }
};
exports.getGuideBySlug = getGuideBySlug;
/**
 * Get guides by category
 */
const getGuidesByCategory = async (req, res) => {
    try {
        const { category } = req.params;
        const { limit } = req.query;
        if (!category) {
            (0, response_util_1.sendError)(res, 'Category is required', null, 400);
            return;
        }
        const validCategories = ['application', 'documents', 'preparation', 'tips'];
        if (!validCategories.includes(category)) {
            (0, response_util_1.sendError)(res, 'Invalid category', null, 400);
            return;
        }
        const guides = await Guide_1.Guide.findByCategory(category, limit ? Number(limit) : undefined);
        (0, response_util_1.sendSuccess)(res, 'Guides retrieved successfully', guides);
    }
    catch (error) {
        console.error('Error fetching guides by category:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch guides', error);
    }
};
exports.getGuidesByCategory = getGuidesByCategory;
/**
 * Search guides
 */
const searchGuides = async (req, res) => {
    try {
        const { q, category, page = 1, limit = 10 } = req.query;
        if (!q || typeof q !== 'string') {
            (0, response_util_1.sendError)(res, 'Search query is required', null, 400);
            return;
        }
        const offset = (Number(page) - 1) * Number(limit);
        const options = {
            category: category,
            limit: Number(limit),
            offset
        };
        const guides = await Guide_1.Guide.search(q, options);
        (0, response_util_1.sendSuccess)(res, 'Search completed successfully', {
            guides,
            query: q,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: guides.length
            }
        });
    }
    catch (error) {
        console.error('Error searching guides:', error);
        (0, response_util_1.sendError)(res, 'Failed to search guides', error);
    }
};
exports.searchGuides = searchGuides;
/**
 * Create new guide (Admin only)
 */
const createGuide = async (req, res) => {
    var _a;
    try {
        const guideData = req.body;
        // Validate guide data
        const validation = (0, validation_util_1.validateGuideData)(guideData);
        if (!validation.isValid) {
            (0, response_util_1.sendError)(res, 'Validation failed', validation.errors, 400);
            return;
        }
        // Add admin info
        guideData.createdByAdmin = ((_a = req.admin) === null || _a === void 0 ? void 0 : _a.id) || null;
        // Generate slug if not provided
        if (!guideData.slug) {
            guideData.slug = guideData.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/^-|-$/g, '');
        }
        const guide = await Guide_1.Guide.create(guideData);
        (0, response_util_1.sendSuccess)(res, 'Guide created successfully', guide, 201);
    }
    catch (error) {
        console.error('Error creating guide:', error);
        (0, response_util_1.sendError)(res, 'Failed to create guide', error);
    }
};
exports.createGuide = createGuide;
/**
 * Update guide (Admin only)
 */
const updateGuide = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        if (!id || isNaN(Number(id))) {
            (0, response_util_1.sendError)(res, 'Invalid guide ID', null, 400);
            return;
        }
        // Check if guide exists
        const existingGuide = await Guide_1.Guide.findById(Number(id));
        if (!existingGuide) {
            (0, response_util_1.sendError)(res, 'Guide not found', null, 404);
            return;
        }
        // Validate update data
        const validation = (0, validation_util_1.validateGuideData)(updateData, true);
        if (!validation.isValid) {
            (0, response_util_1.sendError)(res, 'Validation failed', validation.errors, 400);
            return;
        }
        const guide = await Guide_1.Guide.update(Number(id), updateData);
        if (!guide) {
            (0, response_util_1.sendError)(res, 'Failed to update guide', null, 500);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Guide updated successfully', guide);
    }
    catch (error) {
        console.error('Error updating guide:', error);
        (0, response_util_1.sendError)(res, 'Failed to update guide', error);
    }
};
exports.updateGuide = updateGuide;
/**
 * Delete guide (Admin only)
 */
const deleteGuide = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id || isNaN(Number(id))) {
            (0, response_util_1.sendError)(res, 'Invalid guide ID', null, 400);
            return;
        }
        // Check if guide exists
        const existingGuide = await Guide_1.Guide.findById(Number(id));
        if (!existingGuide) {
            (0, response_util_1.sendError)(res, 'Guide not found', null, 404);
            return;
        }
        const deleted = await Guide_1.Guide.delete(Number(id));
        if (!deleted) {
            (0, response_util_1.sendError)(res, 'Failed to delete guide', null, 500);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Guide deleted successfully', null);
    }
    catch (error) {
        console.error('Error deleting guide:', error);
        (0, response_util_1.sendError)(res, 'Failed to delete guide', error);
    }
};
exports.deleteGuide = deleteGuide;
