"use strict";
/**
 * Opportunity Controller
 *
 * Handles all opportunity-related operations with industry-standard practices
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOpportunityTypesForSidebar = exports.getLatestOpportunities = exports.getOpportunityTypes = exports.deleteOpportunity = exports.updateOpportunity = exports.createOpportunity = exports.searchOpportunities = exports.getOpportunitiesByType = exports.getOpportunityBySlug = exports.getOpportunityById = exports.getActiveOpportunities = exports.getAllOpportunities = void 0;
const Opportunity_1 = require("../models/Opportunity");
const response_util_1 = require("../utils/response.util");
const database_1 = require("../config/database");
const validation_util_1 = require("../utils/validation.util");
/**
 * Get all opportunities with filtering and pagination
 */
const getAllOpportunities = async (req, res) => {
    try {
        const { page = 1, limit = 10, type, location, isRemote, active, orderBy = 'created_at', orderDirection = 'DESC' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const options = {
            limit: Number(limit),
            offset,
            type: type,
            location: location,
            isRemote: isRemote === 'true' ? true : isRemote === 'false' ? false : undefined,
            isActive: active === 'true' ? true : active === 'false' ? false : undefined,
            orderBy: orderBy,
            orderDirection: orderDirection
        };
        const opportunities = await Opportunity_1.Opportunity.findAll(options);
        (0, response_util_1.sendSuccess)(res, 'Opportunities retrieved successfully', opportunities);
    }
    catch (error) {
        console.error('Error fetching opportunities:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch opportunities', error);
    }
};
exports.getAllOpportunities = getAllOpportunities;
/**
 * Get active opportunities (not expired)
 */
const getActiveOpportunities = async (req, res) => {
    try {
        const { limit } = req.query;
        const opportunities = await Opportunity_1.Opportunity.findActive(limit ? Number(limit) : undefined);
        (0, response_util_1.sendSuccess)(res, 'Active opportunities retrieved successfully', opportunities);
    }
    catch (error) {
        console.error('Error fetching active opportunities:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch active opportunities', error);
    }
};
exports.getActiveOpportunities = getActiveOpportunities;
/**
 * Get opportunity by ID
 */
const getOpportunityById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id || isNaN(Number(id))) {
            (0, response_util_1.sendError)(res, 'Invalid opportunity ID', null, 400);
            return;
        }
        const opportunity = await Opportunity_1.Opportunity.findById(Number(id));
        if (!opportunity) {
            (0, response_util_1.sendError)(res, 'Opportunity not found', null, 404);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Opportunity retrieved successfully', opportunity);
    }
    catch (error) {
        console.error('Error fetching opportunity:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch opportunity', error);
    }
};
exports.getOpportunityById = getOpportunityById;
/**
 * Get opportunity by slug
 */
const getOpportunityBySlug = async (req, res) => {
    try {
        const { slug } = req.params;
        if (!slug) {
            (0, response_util_1.sendError)(res, 'Slug is required', null, 400);
            return;
        }
        const opportunity = await Opportunity_1.Opportunity.findBySlug(slug);
        if (!opportunity) {
            (0, response_util_1.sendError)(res, 'Opportunity not found', null, 404);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Opportunity retrieved successfully', opportunity);
    }
    catch (error) {
        console.error('Error getting opportunity by slug:', error);
        (0, response_util_1.sendError)(res, 'Failed to retrieve opportunity', error);
    }
};
exports.getOpportunityBySlug = getOpportunityBySlug;
/**
 * Get opportunities by type with pagination
 */
const getOpportunitiesByType = async (req, res) => {
    try {
        const { type } = req.params;
        const { page = 1, limit = 10 } = req.query;
        if (!type) {
            (0, response_util_1.sendError)(res, 'Type is required', null, 400);
            return;
        }
        const validTypes = ['internship', 'training', 'conference', 'workshop', 'competition'];
        if (!validTypes.includes(type)) {
            (0, response_util_1.sendError)(res, 'Invalid opportunity type', null, 400);
            return;
        }
        const offset = (Number(page) - 1) * Number(limit);
        const options = {
            type,
            isActive: true,
            limit: Number(limit),
            offset,
            orderBy: 'deadline',
            orderDirection: 'ASC'
        };
        const opportunities = await Opportunity_1.Opportunity.findAll(options);
        // Get total count for pagination
        const countResult = await (0, database_1.query)('SELECT COUNT(*) as total FROM opportunities WHERE type = $1 AND is_active = true', [type]);
        const total = parseInt(countResult.rows[0].total);
        const pagination = {
            page: Number(page),
            limit: Number(limit),
            total,
            totalPages: Math.ceil(total / Number(limit)),
            hasNextPage: Number(page) < Math.ceil(total / Number(limit)),
            hasPreviousPage: Number(page) > 1
        };
        (0, response_util_1.sendSuccess)(res, 'Opportunities retrieved successfully', { data: opportunities, pagination });
    }
    catch (error) {
        console.error('Error fetching opportunities by type:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch opportunities', error);
    }
};
exports.getOpportunitiesByType = getOpportunitiesByType;
/**
 * Search opportunities
 */
const searchOpportunities = async (req, res) => {
    try {
        const { q, type, location, isRemote, page = 1, limit = 10 } = req.query;
        if (!q || typeof q !== 'string') {
            (0, response_util_1.sendError)(res, 'Search query is required', null, 400);
            return;
        }
        const offset = (Number(page) - 1) * Number(limit);
        const options = {
            type: type,
            location: location,
            isRemote: isRemote === 'true' ? true : isRemote === 'false' ? false : undefined,
            limit: Number(limit),
            offset
        };
        const opportunities = await Opportunity_1.Opportunity.search(q, options);
        (0, response_util_1.sendSuccess)(res, 'Search completed successfully', {
            opportunities,
            query: q,
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: opportunities.length
            }
        });
    }
    catch (error) {
        console.error('Error searching opportunities:', error);
        (0, response_util_1.sendError)(res, 'Failed to search opportunities', error);
    }
};
exports.searchOpportunities = searchOpportunities;
/**
 * Create new opportunity (Admin only)
 */
const createOpportunity = async (req, res) => {
    var _a;
    try {
        const opportunityData = req.body;
        // Validate opportunity data
        const validation = (0, validation_util_1.validateOpportunityData)(opportunityData);
        if (!validation.isValid) {
            (0, response_util_1.sendError)(res, 'Validation failed', validation.errors, 400);
            return;
        }
        // Add admin info
        opportunityData.createdByAdmin = ((_a = req.admin) === null || _a === void 0 ? void 0 : _a.id) || null;
        // Ensure deadline is a valid Date
        if (opportunityData.deadline && typeof opportunityData.deadline === 'string') {
            try {
                const date = new Date(opportunityData.deadline);
                if (!isNaN(date.getTime())) {
                    opportunityData.deadline = date;
                }
            }
            catch (err) {
                (0, response_util_1.sendError)(res, 'Invalid deadline date format', null, 400);
                return;
            }
        }
        const opportunity = await Opportunity_1.Opportunity.create(opportunityData);
        (0, response_util_1.sendSuccess)(res, 'Opportunity created successfully', opportunity, 201);
    }
    catch (error) {
        console.error('Error creating opportunity:', error);
        (0, response_util_1.sendError)(res, 'Failed to create opportunity', error);
    }
};
exports.createOpportunity = createOpportunity;
/**
 * Update opportunity (Admin only)
 */
const updateOpportunity = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        if (!id || isNaN(Number(id))) {
            (0, response_util_1.sendError)(res, 'Invalid opportunity ID', null, 400);
            return;
        }
        // Check if opportunity exists
        const existingOpportunity = await Opportunity_1.Opportunity.findById(Number(id));
        if (!existingOpportunity) {
            (0, response_util_1.sendError)(res, 'Opportunity not found', null, 404);
            return;
        }
        // Validate update data
        const validation = (0, validation_util_1.validateOpportunityData)(updateData, true);
        if (!validation.isValid) {
            (0, response_util_1.sendError)(res, 'Validation failed', validation.errors, 400);
            return;
        }
        // Ensure deadline is a valid Date if provided
        if (updateData.deadline && typeof updateData.deadline === 'string') {
            try {
                const date = new Date(updateData.deadline);
                if (!isNaN(date.getTime())) {
                    updateData.deadline = date;
                }
                else {
                    (0, response_util_1.sendError)(res, 'Invalid deadline date format', null, 400);
                    return;
                }
            }
            catch (err) {
                (0, response_util_1.sendError)(res, 'Invalid deadline date format', null, 400);
                return;
            }
        }
        const opportunity = await Opportunity_1.Opportunity.update(Number(id), updateData);
        if (!opportunity) {
            (0, response_util_1.sendError)(res, 'Failed to update opportunity', null, 500);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Opportunity updated successfully', opportunity);
    }
    catch (error) {
        console.error('Error updating opportunity:', error);
        (0, response_util_1.sendError)(res, 'Failed to update opportunity', error);
    }
};
exports.updateOpportunity = updateOpportunity;
/**
 * Delete opportunity (Admin only)
 */
const deleteOpportunity = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id || isNaN(Number(id))) {
            (0, response_util_1.sendError)(res, 'Invalid opportunity ID', null, 400);
            return;
        }
        // Check if opportunity exists
        const existingOpportunity = await Opportunity_1.Opportunity.findById(Number(id));
        if (!existingOpportunity) {
            (0, response_util_1.sendError)(res, 'Opportunity not found', null, 404);
            return;
        }
        const deleted = await Opportunity_1.Opportunity.delete(Number(id));
        if (!deleted) {
            (0, response_util_1.sendError)(res, 'Failed to delete opportunity', null, 500);
            return;
        }
        (0, response_util_1.sendSuccess)(res, 'Opportunity deleted successfully', null);
    }
    catch (error) {
        console.error('Error deleting opportunity:', error);
        (0, response_util_1.sendError)(res, 'Failed to delete opportunity', error);
    }
};
exports.deleteOpportunity = deleteOpportunity;
/**
 * Get opportunity types for dropdown menu
 */
const getOpportunityTypes = async (req, res) => {
    try {
        const result = await (0, database_1.query)(`
      SELECT
        type,
        COUNT(*) as count,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
      FROM opportunities
      WHERE type IS NOT NULL AND type != ''
      GROUP BY type
      ORDER BY count DESC
    `);
        const types = result.rows.map(row => ({
            name: row.type,
            count: parseInt(row.count),
            activeCount: parseInt(row.active_count),
            slug: row.type.toLowerCase().replace(/\s+/g, '-')
        }));
        (0, response_util_1.sendSuccess)(res, 'Opportunity types retrieved successfully', types);
    }
    catch (error) {
        console.error('Error fetching opportunity types:', error);
        (0, response_util_1.sendError)(res, 'Failed to fetch opportunity types', error);
    }
};
exports.getOpportunityTypes = getOpportunityTypes;
/**
 * Get latest opportunities for sidebar - Industry Standard Implementation
 */
const getLatestOpportunities = async (req, res) => {
    try {
        const { limit = '6', excludeId } = req.query;
        const limitNumber = Math.min(parseInt(limit, 10), 20);
        let whereClause = 'WHERE 1=1';
        const params = [];
        // Exclude specific opportunity if provided
        if (excludeId) {
            whereClause += ' AND id != $1';
            params.push(parseInt(excludeId, 10));
        }
        const result = await (0, database_1.query)(`
      SELECT
        id, title, type, organization, location, deadline, is_active,
        thumbnail, created_at, start_date, end_date
      FROM opportunities
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);
        const opportunities = result.rows.map(row => ({
            id: row.id,
            title: row.title,
            type: row.type,
            organization: row.organization,
            location: row.location,
            deadline: row.deadline,
            isActive: row.is_active,
            thumbnail: row.thumbnail,
            createdAt: row.created_at,
            startDate: row.start_date,
            endDate: row.end_date
        }));
        return (0, response_util_1.sendSuccess)(res, 'Latest opportunities retrieved successfully', opportunities);
    }
    catch (error) {
        console.error('Error fetching latest opportunities:', error);
        return (0, response_util_1.sendError)(res, 'Failed to fetch latest opportunities', error);
    }
};
exports.getLatestOpportunities = getLatestOpportunities;
/**
 * Get opportunity types for sidebar - Industry Standard Implementation
 */
const getOpportunityTypesForSidebar = async (req, res) => {
    try {
        const { limit = '10', excludeType } = req.query;
        const limitNumber = Math.min(parseInt(limit, 10), 20);
        let whereClause = 'WHERE type IS NOT NULL AND type != \'\'';
        const params = [];
        if (excludeType) {
            whereClause += ' AND type != $1';
            params.push(decodeURIComponent(excludeType));
        }
        const result = await (0, database_1.query)(`
      SELECT
        type,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_count,
        MAX(created_at) as latest_opportunity
      FROM opportunities
      ${whereClause}
      GROUP BY type
      ORDER BY total_count DESC, latest_opportunity DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);
        const types = result.rows.map(row => ({
            name: row.type,
            totalCount: parseInt(row.total_count),
            activeCount: parseInt(row.active_count),
            latestOpportunity: row.latest_opportunity,
            slug: row.type.toLowerCase().replace(/\s+/g, '-')
        }));
        return (0, response_util_1.sendSuccess)(res, 'Opportunity types for sidebar retrieved successfully', types);
    }
    catch (error) {
        console.error('Error fetching opportunity types for sidebar:', error);
        return (0, response_util_1.sendError)(res, 'Failed to fetch opportunity types for sidebar', error);
    }
};
exports.getOpportunityTypesForSidebar = getOpportunityTypesForSidebar;
