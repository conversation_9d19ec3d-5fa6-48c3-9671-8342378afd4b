"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCountriesForSidebar = exports.getRelatedScholarships = exports.getLatestScholarships = exports.getFundingSources = exports.getScholarshipLevels = exports.bulkImportScholarships = exports.deleteScholarship = exports.updateScholarship = exports.getScholarshipById = exports.searchScholarships = exports.getScholarships = exports.createScholarship = void 0;
const express_validator_1 = require("express-validator");
const Scholarship_1 = require("../models/Scholarship");
const Newsletter_1 = require("../models/Newsletter");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const email_1 = require("../utils/email");
const imageService_1 = require("../services/imageService");
const apiResponse_1 = require("../utils/apiResponse");
const dateUtils_1 = __importDefault(require("../utils/dateUtils"));
const database_1 = require("../config/database");
// Using PostgreSQL models directly instead of Prisma
// Create scholarship controller
const createScholarship = async (req, res) => {
    var _a, _b, _c;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        // Log the request body and file
        console.log('Create scholarship request body:', req.body);
        console.log('Create scholarship request file:', req.file);
        // Parse fields that need conversion
        let scholarshipData = {
            ...req.body,
        };
        // Handle created_by field based on user type
        if (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin') {
            // Admin created scholarship
            scholarshipData.createdByAdmin = req.user.id;
            scholarshipData.createdBy = null;
        }
        else {
            // User created scholarship
            scholarshipData.createdBy = (_c = req.user) === null || _c === void 0 ? void 0 : _c.id;
            scholarshipData.createdByAdmin = null;
        }
        // Convert isOpen to boolean if it's a string
        if (typeof scholarshipData.isOpen === 'string') {
            scholarshipData.isOpen = scholarshipData.isOpen === 'true';
        }
        // Ensure deadline is a valid Date
        if (scholarshipData.deadline && typeof scholarshipData.deadline === 'string') {
            try {
                // Try to parse the date
                const date = new Date(scholarshipData.deadline);
                if (!isNaN(date.getTime())) {
                    scholarshipData.deadline = date;
                }
            }
            catch (err) {
                console.error('Error parsing deadline date:', err);
                return res.status(400).json({ message: 'Invalid deadline date format' });
            }
        }
        // Handle thumbnail processing according to industry standards
        // Priority 1: File upload (multipart/form-data) - Industry standard
        if (req.file && !scholarshipData.thumbnail) {
            scholarshipData.thumbnail = `/uploads/scholarships/${req.file.filename}`;
            console.log('Setting thumbnail path from uploaded file:', scholarshipData.thumbnail);
            // Generate thumbnails for the uploaded image
            try {
                const fullPath = path_1.default.join(__dirname, '../../uploads/scholarships', req.file.filename);
                const filenameWithoutExt = req.file.filename.replace(/\.[^/.]+$/, '');
                // Validate and generate thumbnails
                const validation = await imageService_1.ImageService.validateImage(fullPath);
                if (validation.isValid) {
                    const thumbnailResult = await imageService_1.ImageService.generateThumbnails(fullPath, filenameWithoutExt);
                    if (thumbnailResult.success) {
                        console.log('Generated thumbnails for uploaded image:', Object.keys(thumbnailResult.thumbnails || {}));
                    }
                    else {
                        console.error('Failed to generate thumbnails:', thumbnailResult.error);
                    }
                }
                else {
                    console.error('Invalid uploaded image:', validation.error);
                }
            }
            catch (thumbnailError) {
                console.error('Error generating thumbnails for uploaded image:', thumbnailError);
                // Don't fail the scholarship creation if thumbnail generation fails
            }
        }
        // Priority 2: Base64 image conversion to file (for admin interface compatibility)
        else if (scholarshipData.thumbnail && scholarshipData.thumbnail.startsWith('data:image/')) {
            try {
                const base64Data = scholarshipData.thumbnail.split(',')[1];
                const mimeType = scholarshipData.thumbnail.split(';')[0].split(':')[1];
                const extension = mimeType.split('/')[1];
                // Generate secure filename
                const timestamp = Date.now();
                const randomBytes = require('crypto').randomBytes(16).toString('hex');
                const filename = `base64_upload-${timestamp}-${randomBytes}.${extension}`;
                const filePath = path_1.default.join(__dirname, '../../uploads/scholarships', filename);
                // Convert base64 to file and save
                const buffer = Buffer.from(base64Data, 'base64');
                fs_1.default.writeFileSync(filePath, buffer);
                // Update thumbnail path to file URL
                scholarshipData.thumbnail = `/uploads/scholarships/${filename}`;
                console.log('Converted base64 image to file:', scholarshipData.thumbnail);
            }
            catch (error) {
                console.error('Error converting base64 image to file:', error);
                // Remove invalid base64 data to prevent database corruption
                scholarshipData.thumbnail = null;
            }
        }
        console.log('Processed scholarship data for creation:', scholarshipData);
        // Create the scholarship in the database
        const scholarship = await Scholarship_1.Scholarship.create(scholarshipData);
        console.log('Scholarship created successfully:', scholarship.id);
        // Send notification to all newsletter subscribers
        try {
            // Get all newsletter subscribers
            const subscribers = await Newsletter_1.Newsletter.findAll();
            if (subscribers.length > 0) {
                // Send notification in the background (don't await)
                (0, email_1.sendNewScholarshipNotification)(subscribers.map(s => ({ email: s.email })), scholarship.title, scholarship.description || '', scholarship.id).catch(err => {
                    console.error('Error sending scholarship notifications:', err);
                });
                console.log(`Queued notifications to ${subscribers.length} subscribers about new scholarship`);
            }
        }
        catch (notificationError) {
            // Log error but don't fail the scholarship creation
            console.error('Error preparing scholarship notifications:', notificationError);
        }
        res.status(201).json(scholarship);
    }
    catch (error) {
        console.error('Create scholarship error:', error);
        res.status(500).json({ message: 'Server error', error: error.message });
    }
};
exports.createScholarship = createScholarship;
// Get all scholarships controller with pagination
const getScholarships = async (req, res) => {
    try {
        const { level, country, isOpen, page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const where = {};
        // Parse pagination parameters
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const skip = (pageNumber - 1) * limitNumber;
        // Apply filters
        if (level)
            where.level = level;
        if (country)
            where.country = country;
        if (isOpen !== undefined)
            where.isOpen = isOpen === 'true';
        // Validate sortBy field to prevent injection
        const validSortFields = ['createdAt', 'title', 'deadline', 'level', 'country'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        // Validate sortOrder
        const order = (sortOrder === null || sortOrder === void 0 ? void 0 : sortOrder.toLowerCase()) === 'asc' ? 'asc' : 'desc';
        // Get total count for pagination
        const totalCount = await Scholarship_1.Scholarship.count(where);
        // Get paginated scholarships
        const scholarships = await Scholarship_1.Scholarship.findAll({
            limit: limitNumber,
            offset: skip,
            ...where
        });
        // Process scholarships to add computed fields and format dates
        const processedScholarships = scholarships.map((scholarship) => ({
            ...scholarship,
            createdAt: scholarship.createdAt.toISOString(),
            updatedAt: scholarship.updatedAt.toISOString(),
            deadline: scholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(scholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(scholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(scholarship.deadline),
        }));
        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limitNumber);
        // Return paginated response
        return (0, apiResponse_1.sendPaginatedSuccess)(res, processedScholarships, {
            page: pageNumber,
            limit: limitNumber,
            total: totalCount,
            totalPages
        }, 'Scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Get scholarships error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve scholarships', error);
    }
};
exports.getScholarships = getScholarships;
// Search scholarships controller with pagination
const searchScholarships = async (req, res) => {
    try {
        const { q, level, country, isOpen, page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const where = {};
        // Parse pagination parameters
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const skip = (pageNumber - 1) * limitNumber;
        // Apply search and filters
        if (q) {
            // Use more efficient search with indexes
            where.OR = [
                { title: { contains: q, mode: 'insensitive' } },
                { description: { contains: q, mode: 'insensitive' } }
            ];
        }
        // Apply filters
        if (level)
            where.level = level;
        if (country)
            where.country = country;
        if (isOpen !== undefined)
            where.isOpen = isOpen === 'true';
        // Validate sortBy field to prevent injection
        const validSortFields = ['createdAt', 'title', 'deadline', 'level', 'country'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        // Validate sortOrder
        const order = (sortOrder === null || sortOrder === void 0 ? void 0 : sortOrder.toLowerCase()) === 'asc' ? 'asc' : 'desc';
        // Use Promise.all to run queries in parallel for better performance
        const [totalCount, scholarships] = await Promise.all([
            // Get total count for pagination
            Scholarship_1.Scholarship.count(where),
            // Get paginated search results
            Scholarship_1.Scholarship.search(q || '', {
                limit: limitNumber,
                offset: skip,
                ...where
            })
        ]);
        // Process scholarships to add computed fields and format dates
        const processedScholarships = scholarships.map((scholarship) => ({
            ...scholarship,
            createdAt: scholarship.createdAt.toISOString(),
            updatedAt: scholarship.updatedAt.toISOString(),
            deadline: scholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(scholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(scholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(scholarship.deadline),
        }));
        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limitNumber);
        // Return paginated response using standardized format
        return (0, apiResponse_1.sendPaginatedSuccess)(res, processedScholarships, {
            page: pageNumber,
            limit: limitNumber,
            total: totalCount,
            totalPages
        }, 'Scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Search scholarships error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to search scholarships', error);
    }
};
exports.searchScholarships = searchScholarships;
// Get scholarship by ID controller
const getScholarshipById = async (req, res) => {
    var _a, _b;
    try {
        const id = parseInt(req.params.id);
        // Validate ID
        if (isNaN(id) || id <= 0) {
            return (0, apiResponse_1.sendError)(res, 'Invalid scholarship ID', null, 400);
        }
        const scholarship = await Scholarship_1.Scholarship.findById(id);
        if (!scholarship) {
            return (0, apiResponse_1.sendNotFound)(res, 'Scholarship not found');
        }
        // Process dates for consistent format
        const processedScholarship = {
            ...scholarship,
            createdAt: (_a = scholarship.createdAt) === null || _a === void 0 ? void 0 : _a.toISOString(),
            updatedAt: (_b = scholarship.updatedAt) === null || _b === void 0 ? void 0 : _b.toISOString(),
            deadline: scholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(scholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(scholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(scholarship.deadline),
        };
        return (0, apiResponse_1.sendSuccess)(res, processedScholarship, 'Scholarship retrieved successfully');
    }
    catch (error) {
        console.error('Get scholarship by ID error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve scholarship', error);
    }
};
exports.getScholarshipById = getScholarshipById;
// Update scholarship controller
const updateScholarship = async (req, res) => {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        // Log the request body and file
        console.log('Update scholarship request body:', req.body);
        console.log('Update scholarship file:', req.file);
        const scholarshipId = parseInt(req.params.id);
        if (isNaN(scholarshipId)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid scholarship ID', null, 400);
        }
        const scholarship = await Scholarship_1.Scholarship.findById(scholarshipId);
        if (!scholarship) {
            return (0, apiResponse_1.sendNotFound)(res, 'Scholarship not found');
        }
        // Check if user is the creator or an admin
        const isCreator = (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin')
            ? scholarship.createdByAdmin === ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id)
            : scholarship.createdBy === ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id);
        const isAdmin = ((_e = req.user) === null || _e === void 0 ? void 0 : _e.role) === 'admin' || ((_f = req.user) === null || _f === void 0 ? void 0 : _f.role) === 'super_admin';
        if (!isCreator && !isAdmin) {
            return (0, apiResponse_1.sendForbidden)(res, 'Not authorized to update this scholarship');
        }
        // Parse fields that need conversion
        let updateData = {
            ...req.body
        };
        // Convert isOpen to boolean if it's a string
        if (typeof updateData.isOpen === 'string') {
            updateData.isOpen = updateData.isOpen === 'true';
        }
        // Ensure deadline is a valid Date
        if (updateData.deadline && typeof updateData.deadline === 'string') {
            try {
                // Try to parse the date
                const date = new Date(updateData.deadline);
                if (!isNaN(date.getTime())) {
                    updateData.deadline = date;
                }
                else {
                    return (0, apiResponse_1.sendError)(res, 'Invalid deadline date format', null, 400);
                }
            }
            catch (err) {
                console.error('Error parsing deadline date:', err);
                return (0, apiResponse_1.sendError)(res, 'Invalid deadline date format', err, 400);
            }
        }
        // Handle thumbnail processing according to industry standards
        // Priority 1: File upload (multipart/form-data) - Industry standard
        if (req.file) {
            updateData.thumbnail = `/uploads/scholarships/${req.file.filename}`;
            console.log('Setting thumbnail path from uploaded file:', updateData.thumbnail);
            // Generate thumbnails for the uploaded image
            try {
                const fullPath = path_1.default.join(__dirname, '../../uploads/scholarships', req.file.filename);
                const filenameWithoutExt = req.file.filename.replace(/\.[^/.]+$/, '');
                // Validate and generate thumbnails
                const validation = await imageService_1.ImageService.validateImage(fullPath);
                if (validation.isValid) {
                    const thumbnailResult = await imageService_1.ImageService.generateThumbnails(fullPath, filenameWithoutExt);
                    if (thumbnailResult.success) {
                        console.log('Generated thumbnails for updated image:', Object.keys(thumbnailResult.thumbnails || {}));
                    }
                    else {
                        console.error('Failed to generate thumbnails:', thumbnailResult.error);
                    }
                }
                else {
                    console.error('Invalid uploaded image:', validation.error);
                }
            }
            catch (thumbnailError) {
                console.error('Error generating thumbnails for uploaded image:', thumbnailError);
                // Don't fail the scholarship update if thumbnail generation fails
            }
        }
        // Priority 2: Base64 image conversion to file (for admin interface compatibility)
        else if (updateData.thumbnail && updateData.thumbnail.startsWith('data:image/')) {
            try {
                const base64Data = updateData.thumbnail.split(',')[1];
                const mimeType = updateData.thumbnail.split(';')[0].split(':')[1];
                const extension = mimeType.split('/')[1];
                // Generate secure filename
                const timestamp = Date.now();
                const randomBytes = require('crypto').randomBytes(16).toString('hex');
                const filename = `base64_upload-${timestamp}-${randomBytes}.${extension}`;
                const filePath = path_1.default.join(__dirname, '../../uploads/scholarships', filename);
                // Convert base64 to file and save
                const buffer = Buffer.from(base64Data, 'base64');
                fs_1.default.writeFileSync(filePath, buffer);
                // Update thumbnail path to file URL
                updateData.thumbnail = `/uploads/scholarships/${filename}`;
                console.log('Converted base64 image to file:', updateData.thumbnail);
            }
            catch (error) {
                console.error('Error converting base64 image to file:', error);
                // Keep existing thumbnail if conversion fails
                delete updateData.thumbnail;
            }
        }
        console.log('Processed update data:', updateData);
        // Clean up old thumbnail files according to industry standards
        // Only delete files, not base64 data (which shouldn't exist in production)
        if (updateData.thumbnail && scholarship.thumbnail &&
            updateData.thumbnail !== scholarship.thumbnail &&
            scholarship.thumbnail.startsWith('/uploads/')) {
            try {
                // Get the file path relative to the project root
                const oldFilePath = path_1.default.join(__dirname, '../..', scholarship.thumbnail);
                // Check if file exists before attempting to delete
                if (fs_1.default.existsSync(oldFilePath)) {
                    fs_1.default.unlinkSync(oldFilePath);
                    console.log(`Deleted old thumbnail file: ${oldFilePath}`);
                }
            }
            catch (err) {
                // Log error but don't fail the update
                console.error('Error deleting old thumbnail file:', err);
            }
        }
        // Log warning if old thumbnail was base64 (should not happen in production)
        else if (scholarship.thumbnail && scholarship.thumbnail.startsWith('data:image/')) {
            console.warn('Warning: Found base64 thumbnail in database. This violates industry standards and should be migrated to file storage.');
        }
        const updatedScholarship = await Scholarship_1.Scholarship.update(scholarshipId, updateData);
        if (!updatedScholarship) {
            return (0, apiResponse_1.sendError)(res, 'Failed to update scholarship', null, 500);
        }
        // Process dates for consistent format
        const processedScholarship = {
            ...updatedScholarship,
            createdAt: (_g = updatedScholarship.createdAt) === null || _g === void 0 ? void 0 : _g.toISOString(),
            updatedAt: (_h = updatedScholarship.updatedAt) === null || _h === void 0 ? void 0 : _h.toISOString(),
            deadline: updatedScholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(updatedScholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(updatedScholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(updatedScholarship.deadline),
        };
        console.log('Scholarship updated successfully:', updatedScholarship.id);
        return (0, apiResponse_1.sendSuccess)(res, processedScholarship, 'Scholarship updated successfully');
    }
    catch (error) {
        console.error('Update scholarship error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to update scholarship', error);
    }
};
exports.updateScholarship = updateScholarship;
// Delete scholarship controller
const deleteScholarship = async (req, res) => {
    var _a, _b, _c, _d, _e, _f;
    try {
        const scholarshipId = parseInt(req.params.id);
        if (isNaN(scholarshipId) || scholarshipId <= 0) {
            return (0, apiResponse_1.sendError)(res, 'Invalid scholarship ID', null, 400);
        }
        const scholarship = await Scholarship_1.Scholarship.findById(scholarshipId);
        if (!scholarship) {
            return (0, apiResponse_1.sendNotFound)(res, 'Scholarship not found');
        }
        // Check if user is the creator or an admin
        const isCreator = (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin')
            ? scholarship.createdByAdmin === ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id)
            : scholarship.createdBy === ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id);
        const isAdmin = ((_e = req.user) === null || _e === void 0 ? void 0 : _e.role) === 'admin' || ((_f = req.user) === null || _f === void 0 ? void 0 : _f.role) === 'super_admin';
        if (!isCreator && !isAdmin) {
            return (0, apiResponse_1.sendForbidden)(res, 'Not authorized to delete this scholarship');
        }
        // Clean up thumbnail files according to industry standards
        if (scholarship.thumbnail && scholarship.thumbnail.startsWith('/uploads/')) {
            try {
                // Get the file path relative to the project root
                const filePath = path_1.default.join(__dirname, '../..', scholarship.thumbnail);
                // Check if file exists before attempting to delete
                if (fs_1.default.existsSync(filePath)) {
                    fs_1.default.unlinkSync(filePath);
                    console.log(`Deleted thumbnail file for scholarship ${scholarship.id}: ${filePath}`);
                }
            }
            catch (err) {
                // Log error but don't fail the delete operation
                console.error('Error deleting thumbnail file:', err);
            }
        }
        // Log warning if thumbnail was base64 (should not happen in production)
        else if (scholarship.thumbnail && scholarship.thumbnail.startsWith('data:image/')) {
            console.warn(`Warning: Scholarship ${scholarship.id} had base64 thumbnail. This violates industry standards.`);
        }
        // Delete the scholarship from the database
        await Scholarship_1.Scholarship.delete(scholarshipId);
        // Invalidate cache for scholarships
        try {
            const apiCache = require('../middleware/apiCache.middleware').default;
            apiCache.invalidateCache('/api/scholarships');
        }
        catch (cacheError) {
            console.error('Error invalidating cache:', cacheError);
        }
        return (0, apiResponse_1.sendSuccess)(res, { id: scholarshipId }, 'Scholarship deleted successfully');
    }
    catch (error) {
        console.error('Delete scholarship error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to delete scholarship', error);
    }
};
exports.deleteScholarship = deleteScholarship;
// Bulk import scholarships controller
const bulkImportScholarships = async (req, res) => {
    var _a, _b, _c;
    try {
        // Validate that we have an array of scholarships
        const { scholarships } = req.body;
        if (!Array.isArray(scholarships) || scholarships.length === 0) {
            return (0, apiResponse_1.sendError)(res, 'Invalid request format', 'Expected an array of scholarships', 400);
        }
        console.log(`Processing bulk import of ${scholarships.length} scholarships`);
        // Track results
        const results = {
            success: 0,
            failures: 0,
            details: []
        };
        // Process each scholarship
        for (const scholarshipData of scholarships) {
            try {
                // Basic validation
                if (!scholarshipData.title || !scholarshipData.description || !scholarshipData.deadline) {
                    results.failures++;
                    results.details.push({
                        title: scholarshipData.title || 'Unknown',
                        status: 'error',
                        message: 'Missing required fields (title, description, or deadline)'
                    });
                    continue;
                }
                // Prepare data for database
                const processedData = {
                    ...scholarshipData,
                };
                // Handle created_by field based on user type
                if (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin') {
                    // Admin created scholarship
                    processedData.createdByAdmin = req.user.id;
                    processedData.createdBy = null;
                }
                else {
                    // User created scholarship
                    processedData.createdBy = (_c = req.user) === null || _c === void 0 ? void 0 : _c.id;
                    processedData.createdByAdmin = null;
                }
                // Convert isOpen to boolean if it's a string
                if (typeof processedData.isOpen === 'string') {
                    processedData.isOpen = processedData.isOpen === 'true';
                }
                else if (processedData.isOpen === undefined) {
                    processedData.isOpen = true; // Default to true if not specified
                }
                // Ensure deadline is a valid Date
                if (processedData.deadline && typeof processedData.deadline === 'string') {
                    try {
                        const date = new Date(processedData.deadline);
                        if (isNaN(date.getTime())) {
                            throw new Error('Invalid date format');
                        }
                        processedData.deadline = date;
                    }
                    catch (err) {
                        results.failures++;
                        results.details.push({
                            title: processedData.title,
                            status: 'error',
                            message: 'Invalid deadline date format'
                        });
                        continue;
                    }
                }
                // Create the scholarship in the database
                const scholarship = await Scholarship_1.Scholarship.create(processedData);
                results.success++;
                results.details.push({
                    title: scholarship.title,
                    status: 'success',
                    id: scholarship.id
                });
                console.log(`Successfully imported scholarship: ${scholarship.title} (ID: ${scholarship.id})`);
            }
            catch (error) {
                console.error(`Error importing scholarship ${scholarshipData.title}:`, error);
                results.failures++;
                results.details.push({
                    title: scholarshipData.title || 'Unknown',
                    status: 'error',
                    message: error.message
                });
            }
        }
        // Invalidate cache for scholarships if any were successfully imported
        if (results.success > 0) {
            try {
                const apiCache = require('../middleware/apiCache.middleware').default;
                apiCache.invalidateCache('/api/scholarships');
            }
            catch (cacheError) {
                console.error('Error invalidating cache:', cacheError);
            }
        }
        // Return results using standardized format
        return (0, apiResponse_1.sendSuccess)(res, {
            results,
            summary: {
                total: scholarships.length,
                success: results.success,
                failures: results.failures
            }
        }, `Bulk import completed. ${results.success} successful, ${results.failures} failed.`);
    }
    catch (error) {
        console.error('Bulk import error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to process bulk import', error);
    }
};
exports.bulkImportScholarships = bulkImportScholarships;
/**
 * Get scholarship levels for dropdown menu
 */
const getScholarshipLevels = async (req, res) => {
    try {
        const result = await (0, database_1.query)(`
      SELECT
        level,
        COUNT(*) as count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count
      FROM scholarships
      WHERE level IS NOT NULL AND level != ''
      GROUP BY level
      ORDER BY count DESC
    `);
        // Normalize and group similar levels
        const levelGroups = {};
        result.rows.forEach(row => {
            const level = row.level.toLowerCase();
            let normalizedLevel = '';
            if (level.includes('licence') || level.includes('undergraduate') || level.includes('bachelor')) {
                normalizedLevel = 'Licence';
            }
            else if (level.includes('master') || (level.includes('graduate') && !level.includes('undergraduate'))) {
                normalizedLevel = 'Master';
            }
            else if (level.includes('doctorat') || level.includes('doctorate') || level.includes('phd')) {
                normalizedLevel = 'Doctorat';
            }
            else {
                // Keep original for unknown levels
                normalizedLevel = row.level;
            }
            if (levelGroups[normalizedLevel]) {
                levelGroups[normalizedLevel].count += parseInt(row.count);
                levelGroups[normalizedLevel].openCount += parseInt(row.open_count);
            }
            else {
                levelGroups[normalizedLevel] = {
                    name: normalizedLevel,
                    count: parseInt(row.count),
                    openCount: parseInt(row.open_count),
                    slug: normalizedLevel.toLowerCase().replace(/\s+/g, '-')
                };
            }
        });
        // Convert to array and sort by French education order
        const levelOrder = ['Licence', 'Master', 'Doctorat'];
        const levels = Object.values(levelGroups).sort((a, b) => {
            const aIndex = levelOrder.indexOf(a.name);
            const bIndex = levelOrder.indexOf(b.name);
            if (aIndex !== -1 && bIndex !== -1) {
                return aIndex - bIndex;
            }
            else if (aIndex !== -1) {
                return -1;
            }
            else if (bIndex !== -1) {
                return 1;
            }
            else {
                return b.count - a.count; // Sort unknown levels by count
            }
        });
        return (0, apiResponse_1.sendSuccess)(res, levels, 'Scholarship levels retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching scholarship levels:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch scholarship levels', error);
    }
};
exports.getScholarshipLevels = getScholarshipLevels;
/**
 * Get funding sources for dropdown menu
 */
const getFundingSources = async (req, res) => {
    try {
        const result = await (0, database_1.query)(`
      SELECT
        funding_source,
        COUNT(*) as count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count
      FROM scholarships
      WHERE funding_source IS NOT NULL AND funding_source != ''
      GROUP BY funding_source
      ORDER BY count DESC
    `);
        const sources = result.rows.map(row => ({
            name: row.funding_source,
            count: parseInt(row.count),
            openCount: parseInt(row.open_count),
            slug: row.funding_source.toLowerCase().replace(/\s+/g, '-')
        }));
        return (0, apiResponse_1.sendSuccess)(res, sources, 'Funding sources retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching funding sources:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch funding sources', error);
    }
};
exports.getFundingSources = getFundingSources;
/**
 * Get latest scholarships for sidebar - Industry Standard Implementation
 */
const getLatestScholarships = async (req, res) => {
    try {
        const { limit = '6', excludeId } = req.query;
        const limitNumber = Math.min(parseInt(limit, 10), 20); // Max 20 for performance
        let whereClause = 'WHERE 1=1';
        const params = [];
        // Exclude specific scholarship if provided (for detail pages)
        if (excludeId) {
            whereClause += ' AND id != $1';
            params.push(parseInt(excludeId, 10));
        }
        const result = await (0, database_1.query)(`
      SELECT
        id, title, country, level, deadline, is_open, thumbnail,
        created_at
      FROM scholarships
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);
        const scholarships = result.rows.map(row => ({
            id: row.id,
            title: row.title,
            country: row.country,
            level: row.level,
            deadline: row.deadline,
            isOpen: row.is_open,
            thumbnail: row.thumbnail,
            createdAt: row.created_at,
            // Add computed fields for frontend
            isExpired: dateUtils_1.default.isDatePast(row.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(row.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(row.deadline)
        }));
        return (0, apiResponse_1.sendSuccess)(res, scholarships, 'Latest scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching latest scholarships:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch latest scholarships', error);
    }
};
exports.getLatestScholarships = getLatestScholarships;
/**
 * Get related scholarships for sidebar - Industry Standard Implementation
 */
const getRelatedScholarships = async (req, res) => {
    try {
        const { country, level, excludeId, limit = '5' } = req.query;
        const limitNumber = Math.min(parseInt(limit, 10), 10);
        if (!country && !level) {
            return (0, apiResponse_1.sendError)(res, 'Either country or level parameter is required', null, 400);
        }
        let whereClause = 'WHERE 1=1';
        const params = [];
        let paramIndex = 1;
        // Exclude current scholarship
        if (excludeId) {
            whereClause += ` AND id != $${paramIndex}`;
            params.push(parseInt(excludeId, 10));
            paramIndex++;
        }
        // Filter by country or level
        if (country) {
            whereClause += ` AND country = $${paramIndex}`;
            params.push(country);
            paramIndex++;
        }
        if (level) {
            whereClause += ` AND level = $${paramIndex}`;
            params.push(level);
            paramIndex++;
        }
        const result = await (0, database_1.query)(`
      SELECT
        id, title, country, level, deadline, is_open, thumbnail
      FROM scholarships
      ${whereClause}
      AND is_open = true
      ORDER BY created_at DESC
      LIMIT $${paramIndex}
    `, [...params, limitNumber]);
        const scholarships = result.rows.map(row => ({
            id: row.id,
            title: row.title,
            country: row.country,
            level: row.level,
            deadline: row.deadline,
            isOpen: row.is_open,
            thumbnail: row.thumbnail,
            fundingSource: row.funding_source,
            // Add computed fields for frontend
            isExpired: dateUtils_1.default.isDatePast(row.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(row.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(row.deadline)
        }));
        return (0, apiResponse_1.sendSuccess)(res, scholarships, 'Related scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching related scholarships:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch related scholarships', error);
    }
};
exports.getRelatedScholarships = getRelatedScholarships;
/**
 * Get countries with enhanced data for sidebar - Industry Standard Implementation
 */
const getCountriesForSidebar = async (req, res) => {
    try {
        const { limit = '15', excludeCountry } = req.query;
        const limitNumber = Math.min(parseInt(limit, 10), 30);
        let whereClause = 'WHERE country IS NOT NULL AND country != \'\'';
        const params = [];
        if (excludeCountry) {
            whereClause += ' AND country != $1';
            params.push(decodeURIComponent(excludeCountry));
        }
        const result = await (0, database_1.query)(`
      SELECT
        country,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_open = true THEN 1 END) as open_count,
        MAX(created_at) as latest_scholarship
      FROM scholarships
      ${whereClause}
      GROUP BY country
      ORDER BY total_count DESC, latest_scholarship DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);
        const countries = result.rows.map(row => ({
            name: row.country,
            totalCount: parseInt(row.total_count),
            openCount: parseInt(row.open_count),
            latestScholarship: row.latest_scholarship,
            slug: row.country.toLowerCase().replace(/\s+/g, '-')
        }));
        return (0, apiResponse_1.sendSuccess)(res, countries, 'Countries for sidebar retrieved successfully');
    }
    catch (error) {
        console.error('Error fetching countries for sidebar:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to fetch countries for sidebar', error);
    }
};
exports.getCountriesForSidebar = getCountriesForSidebar;
