"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const email_1 = require("../utils/email");
const Message_1 = require("../models/Message");
const auth_new_1 = require("../middleware/auth.new");
const router = (0, express_1.Router)();
// Get all messages (admin only)
router.get('/', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const messages = await Message_1.Message.findAll();
        res.json({
            success: true,
            data: messages
        });
    }
    catch (error) {
        console.error('Error fetching messages:', error);
        res.status(500).json({
            success: false,
            error: 'Error fetching messages'
        });
    }
});
// Create a new message
router.post('/', async (req, res) => {
    try {
        const { name, email, subject, content } = req.body;
        // Validate required fields
        if (!name || !email || !subject || !content) {
            return res.status(400).json({ error: 'All fields are required' });
        }
        // Create the message
        const message = await Message_1.Message.create({
            name,
            email,
            subject,
            content,
            status: 'pending',
        });
        console.log(`New message created from ${name} (${email}): ${subject}`);
        res.status(201).json(message);
    }
    catch (error) {
        console.error('Error creating message:', error);
        res.status(500).json({ error: 'Error creating message' });
    }
});
// Get a single message
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const message = await Message_1.Message.findById(Number(id));
        if (!message) {
            return res.status(404).json({ error: 'Message not found' });
        }
        res.json(message);
    }
    catch (error) {
        console.error('Error fetching message:', error);
        res.status(500).json({ error: 'Error fetching message' });
    }
});
// Update message status (admin only)
router.put('/:id', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        if (!status || !['pending', 'read', 'replied'].includes(status)) {
            return res.status(400).json({ error: 'Invalid status value' });
        }
        const message = await Message_1.Message.update(Number(id), { status });
        console.log(`Message ${id} status updated to ${status}`);
        res.json(message);
    }
    catch (error) {
        console.error('Error updating message:', error);
        res.status(500).json({ error: 'Error updating message' });
    }
});
// Delete a message (admin only)
router.delete('/:id', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;
        await Message_1.Message.delete(Number(id));
        res.json({ message: 'Message deleted successfully' });
    }
    catch (error) {
        res.status(500).json({ error: 'Error deleting message' });
    }
});
// Reply to a message (admin only)
router.post('/:id/reply', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;
        const { reply } = req.body;
        if (!reply || reply.trim() === '') {
            return res.status(400).json({ error: 'Reply content is required' });
        }
        // Get the message to reply to
        const message = await Message_1.Message.findById(Number(id));
        if (!message) {
            return res.status(404).json({ error: 'Message not found' });
        }
        // Update message status to replied
        await Message_1.Message.update(Number(id), { status: 'replied' });
        // Send email reply
        try {
            await (0, email_1.sendEmail)({
                to: message.email,
                subject: `Re: ${message.subject}`,
                text: `Dear ${message.name},\n\n${reply}\n\nRegards,\nMaBourse Team`,
                html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #4a6ee0;">Response to Your Message</h2>
            <p>Dear ${message.name},</p>
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="white-space: pre-wrap;">${reply}</p>
            </div>
            <p>Original message: <em>${message.subject}</em></p>
            <p>Regards,<br>MaBourse Team</p>
          </div>
        `
            });
            console.log(`Reply sent to ${message.email}`);
            res.json({
                success: true,
                message: 'Reply sent successfully',
                messageId: message.id
            });
        }
        catch (emailError) {
            console.error('Error sending email reply:', emailError);
            // Still return success but with a warning
            res.status(200).json({
                success: true,
                warning: 'Message status updated but email delivery failed',
                messageId: message.id
            });
        }
    }
    catch (error) {
        console.error('Error replying to message:', error);
        res.status(500).json({ error: 'Error replying to message' });
    }
});
exports.default = router;
