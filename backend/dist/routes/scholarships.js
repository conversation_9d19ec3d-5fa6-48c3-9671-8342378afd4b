"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Scholarship_1 = require("../models/Scholarship");
const auth_new_1 = require("../middleware/auth.new");
const router = (0, express_1.Router)();
// Get all scholarships
router.get('/', async (req, res) => {
    try {
        const scholarships = await Scholarship_1.Scholarship.findAll();
        res.json(scholarships);
    }
    catch (error) {
        res.status(500).json({ error: 'Error fetching scholarships' });
    }
});
// Get a single scholarship
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const scholarship = await Scholarship_1.Scholarship.findById(Number(id));
        if (!scholarship) {
            return res.status(404).json({ error: 'Scholarship not found' });
        }
        res.json(scholarship);
    }
    catch (error) {
        res.status(500).json({ error: 'Error fetching scholarship' });
    }
});
// Bulk import scholarships (admin only)
router.post('/bulk-import', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { scholarships } = req.body;
        if (!Array.isArray(scholarships)) {
            return res.status(400).json({
                success: false,
                message: 'Scholarships must be an array'
            });
        }
        if (scholarships.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'No scholarships provided'
            });
        }
        let imported = 0;
        const errors = [];
        for (let i = 0; i < scholarships.length; i++) {
            const scholarshipData = scholarships[i];
            try {
                // Validate required fields
                if (!scholarshipData.title || !scholarshipData.description || !scholarshipData.deadline) {
                    errors.push(`Scholarship ${i + 1}: Missing required fields (title, description, deadline)`);
                    continue;
                }
                // Create scholarship
                const scholarship = await Scholarship_1.Scholarship.create({
                    title: scholarshipData.title,
                    description: scholarshipData.description,
                    deadline: new Date(scholarshipData.deadline),
                    level: scholarshipData.level_of_study || scholarshipData.level || '',
                    country: scholarshipData.country || '',
                    isOpen: scholarshipData.is_active !== false, // Default to true
                    thumbnail: scholarshipData.thumbnail || '',
                    coverage: scholarshipData.coverage || '',
                    financialBenefitsSummary: scholarshipData.financial_benefits_summary || '',
                    eligibilitySummary: scholarshipData.eligibility_summary || scholarshipData.eligibility || '',
                    scholarshipLink: scholarshipData.scholarship_link || scholarshipData.application_url || '',
                    youtubeLink: scholarshipData.youtube_link || '',
                    domainesEtudes: scholarshipData.field_of_study || '',
                    universitesParticipantes: scholarshipData.organization || '',
                    documentsRequis: scholarshipData.requirements || '',
                    conditionsEligibilite: scholarshipData.eligibility || '',
                    lienPostuler: scholarshipData.application_url || scholarshipData.scholarship_link || '',
                    chaineYoutubeReseaux: scholarshipData.youtube_link || '',
                    createdBy: 1 // Default admin user
                });
                imported++;
            }
            catch (error) {
                errors.push(`Scholarship ${i + 1}: ${error.message}`);
            }
        }
        res.json({
            success: true,
            message: `Bulk import completed. ${imported} scholarships imported successfully.`,
            data: {
                imported,
                total: scholarships.length,
                errors: errors.length > 0 ? errors : undefined
            }
        });
    }
    catch (error) {
        console.error('Bulk import error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to process bulk import',
            error: error.message
        });
    }
});
// Create a new scholarship (admin only)
router.post('/', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { title, description, amount, deadline, eligibility, requirements, application_url, contact_email, organization, field_of_study, level_of_study, country, coverage, financial_benefits_summary, eligibility_summary, scholarship_link, youtube_link, thumbnail, is_active } = req.body;
        // Validate required fields
        if (!title || !description || !deadline) {
            return res.status(400).json({
                success: false,
                message: 'Title, description, and deadline are required'
            });
        }
        const scholarship = await Scholarship_1.Scholarship.create({
            title,
            description,
            deadline: new Date(deadline),
            level: level_of_study || '',
            country: country || '',
            isOpen: is_active !== false, // Default to true
            thumbnail: thumbnail || '',
            coverage: coverage || '',
            financialBenefitsSummary: financial_benefits_summary || '',
            eligibilitySummary: eligibility_summary || eligibility || '',
            scholarshipLink: scholarship_link || application_url || '',
            youtubeLink: youtube_link || '',
            domainesEtudes: field_of_study || '',
            universitesParticipantes: organization || '',
            documentsRequis: requirements || '',
            conditionsEligibilite: eligibility || '',
            lienPostuler: application_url || scholarship_link || '',
            chaineYoutubeReseaux: youtube_link || '',
            createdBy: 1 // Default admin user
        });
        res.json({
            success: true,
            message: 'Scholarship created successfully',
            data: scholarship
        });
    }
    catch (error) {
        console.error('Error creating scholarship:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating scholarship',
            error: error.message
        });
    }
});
// Update a scholarship (admin only)
router.put('/:id', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description, amount, deadline, eligibility, requirements, application_url, contact_email, organization, field_of_study, level_of_study, country, coverage, financial_benefits_summary, eligibility_summary, scholarship_link, youtube_link, thumbnail, is_active } = req.body;
        // Validate required fields
        if (!title || !description || !deadline) {
            return res.status(400).json({
                success: false,
                message: 'Title, description, and deadline are required'
            });
        }
        const scholarship = await Scholarship_1.Scholarship.update(Number(id), {
            title,
            description,
            deadline: new Date(deadline),
            level: level_of_study || '',
            country: country || '',
            isOpen: is_active !== false,
            thumbnail: thumbnail || '',
            coverage: coverage || '',
            financialBenefitsSummary: financial_benefits_summary || '',
            eligibilitySummary: eligibility_summary || eligibility || '',
            scholarshipLink: scholarship_link || application_url || '',
            youtubeLink: youtube_link || '',
            domainesEtudes: field_of_study || '',
            universitesParticipantes: organization || '',
            documentsRequis: requirements || '',
            conditionsEligibilite: eligibility || '',
            lienPostuler: application_url || scholarship_link || '',
            chaineYoutubeReseaux: youtube_link || ''
        });
        res.json({
            success: true,
            message: 'Scholarship updated successfully',
            data: scholarship
        });
    }
    catch (error) {
        console.error('Error updating scholarship:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating scholarship',
            error: error.message
        });
    }
});
// Delete a scholarship (admin only)
router.delete('/:id', auth_new_1.authenticate, auth_new_1.requireAdmin, async (req, res) => {
    try {
        const { id } = req.params;
        await Scholarship_1.Scholarship.delete(Number(id));
        res.json({
            success: true,
            message: 'Scholarship deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting scholarship:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting scholarship',
            error: error.message
        });
    }
});
exports.default = router;
