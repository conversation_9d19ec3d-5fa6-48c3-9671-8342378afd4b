/**
 * Reset Admin Password Script
 * 
 * This script resets the admin password to ensure login works correctly
 */

const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

const pool = new Pool({
  user: 'postgres',
  host: 'localhost', 
  database: 'mabourse',
  password: 'postgres',
  port: 5432
});

async function resetAdminPassword() {
  try {
    console.log('🔧 Resetting admin password...\n');
    
    // Check current admin
    const adminResult = await pool.query('SELECT * FROM admins WHERE email = $1', ['<EMAIL>']);
    
    if (adminResult.rows.length === 0) {
      console.log('❌ Admin user not found!');
      console.log('🔧 Creating new admin user...');
      
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      await pool.query(`
        INSERT INTO admins (name, email, password, role, is_main_admin, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      `, ['Main Administrator', '<EMAIL>', hashedPassword, 'super_admin', true]);
      
      console.log('✅ Admin user created successfully!');
    } else {
      const admin = adminResult.rows[0];
      console.log('👤 Current admin found:');
      console.log(`   📧 Email: ${admin.email}`);
      console.log(`   👤 Name: ${admin.name}`);
      console.log(`   🔑 Role: ${admin.role}`);
      console.log(`   🔐 Main Admin: ${admin.is_main_admin}`);
      
      // Test current password
      const isCurrentPasswordValid = await bcrypt.compare('admin123', admin.password);
      console.log(`   🔒 Current Password Valid: ${isCurrentPasswordValid ? '✅ YES' : '❌ NO'}`);
      
      if (!isCurrentPasswordValid) {
        console.log('\n🔧 Updating password to "admin123"...');
        
        const newHashedPassword = await bcrypt.hash('admin123', 12);
        
        await pool.query(`
          UPDATE admins
          SET password = $1,
              updated_at = NOW()
          WHERE email = $2
        `, [newHashedPassword, '<EMAIL>']);
        
        console.log('✅ Password updated successfully!');
        
        // Verify the new password
        const verifyResult = await pool.query('SELECT password FROM admins WHERE email = $1', ['<EMAIL>']);
        const isNewPasswordValid = await bcrypt.compare('admin123', verifyResult.rows[0].password);
        console.log(`✅ New Password Verification: ${isNewPasswordValid ? '✅ VALID' : '❌ INVALID'}`);
      } else {
        console.log('\n✅ Password is already correct!');
        
        // Just update the timestamp
        await pool.query(`
          UPDATE admins
          SET updated_at = NOW()
          WHERE email = $1
        `, ['<EMAIL>']);

        console.log('✅ Admin record updated');
      }
    }
    
    console.log('\n🎯 ADMIN LOGIN CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('\n✅ Admin login should now work correctly!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  resetAdminPassword()
    .then(() => {
      console.log('\n🎉 Admin password reset completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Password reset failed:', error);
      process.exit(1);
    });
}

module.exports = { resetAdminPassword };
