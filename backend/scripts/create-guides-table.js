/**
 * Create Guides Table Migration
 * 
 * This script creates the guides table in the database if it doesn't exist
 */

const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'mabourse',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

/**
 * Create guides table
 */
async function createGuidesTable() {
  try {
    console.log('🔍 Checking if guides table exists...');
    
    // Check if table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'guides'
      );
    `);
    
    if (tableExists.rows[0].exists) {
      console.log('✅ Guides table already exists');
      return;
    }
    
    console.log('📝 Creating guides table...');
    
    // Create guides table
    await pool.query(`
      CREATE TABLE guides (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        category VARCHAR(50) NOT NULL CHECK (category IN ('application', 'documents', 'preparation', 'tips')),
        slug VARCHAR(255) UNIQUE NOT NULL,
        excerpt TEXT,
        thumbnail VARCHAR(500),
        is_published BOOLEAN DEFAULT true,
        read_time INTEGER, -- in minutes
        tags JSONB,
        created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
        created_by_admin INTEGER REFERENCES admins(id) ON DELETE SET NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      );
    `);
    
    // Create indexes for better performance
    await pool.query(`
      CREATE INDEX idx_guides_category ON guides(category);
      CREATE INDEX idx_guides_published ON guides(is_published);
      CREATE INDEX idx_guides_slug ON guides(slug);
      CREATE INDEX idx_guides_created_at ON guides(created_at);
    `);
    
    console.log('✅ Guides table created successfully');
    
    // Insert some sample guides
    console.log('📝 Inserting sample guides...');
    
    const sampleGuides = [
      {
        title: 'Comment rédiger un CV parfait pour les bourses',
        content: `# Comment rédiger un CV parfait pour les bourses

## Introduction
Un CV bien rédigé est essentiel pour obtenir une bourse d'études. Voici nos conseils pour créer un CV qui se démarque.

## Structure recommandée

### 1. Informations personnelles
- Nom complet
- Adresse email professionnelle
- Numéro de téléphone
- Adresse (ville, pays)

### 2. Objectif professionnel
Rédigez un objectif clair et concis qui explique vos aspirations académiques et professionnelles.

### 3. Formation académique
Listez vos diplômes en ordre chronologique inverse, en incluant :
- Nom de l'établissement
- Diplôme obtenu
- Dates
- Mention ou moyenne (si excellente)

### 4. Expérience professionnelle
Même les stages et emplois étudiants comptent ! Décrivez vos responsabilités et réalisations.

### 5. Compétences
- Langues parlées (avec niveau)
- Compétences techniques
- Compétences transversales

### 6. Activités extrascolaires
Montrez votre engagement communautaire et vos centres d'intérêt.

## Conseils pratiques
- Gardez votre CV sur 1-2 pages maximum
- Utilisez une police lisible (Arial, Calibri)
- Vérifiez l'orthographe et la grammaire
- Adaptez votre CV à chaque candidature

## Erreurs à éviter
- Photo non professionnelle
- Informations personnelles sensibles
- Mensonges ou exagérations
- Format peu professionnel`,
        category: 'documents',
        slug: 'rediger-cv-parfait-bourses',
        excerpt: 'Découvrez comment créer un CV professionnel qui maximise vos chances d\'obtenir une bourse d\'études.',
        readTime: 8,
        tags: ['CV', 'candidature', 'documents', 'conseils']
      },
      {
        title: 'Lettre de motivation : les clés du succès',
        content: `# Lettre de motivation : les clés du succès

## Pourquoi la lettre de motivation est-elle importante ?
La lettre de motivation est votre chance de vous démarquer des autres candidats. Elle permet aux comités de sélection de comprendre vos motivations et votre personnalité.

## Structure d'une lettre de motivation efficace

### 1. En-tête
- Vos coordonnées
- Coordonnées du destinataire
- Date et lieu

### 2. Objet
Soyez précis : "Candidature pour la bourse [nom de la bourse]"

### 3. Introduction
- Présentez-vous brièvement
- Mentionnez la bourse pour laquelle vous postulez
- Captez l'attention avec un élément marquant

### 4. Développement (2-3 paragraphes)
- Expliquez pourquoi vous méritez cette bourse
- Décrivez vos réalisations et expériences pertinentes
- Montrez votre connaissance du programme/pays
- Expliquez vos objectifs futurs

### 5. Conclusion
- Réaffirmez votre motivation
- Remerciez le lecteur
- Proposez un entretien

## Conseils de rédaction
- Soyez authentique et personnel
- Utilisez des exemples concrets
- Évitez les généralités
- Respectez la longueur demandée (généralement 1 page)
- Relisez plusieurs fois

## Erreurs courantes
- Lettre trop générique
- Fautes d'orthographe
- Répétition du CV
- Ton trop familier ou trop formel`,
        category: 'documents',
        slug: 'lettre-motivation-cles-succes',
        excerpt: 'Apprenez à rédiger une lettre de motivation convaincante qui fera la différence dans votre candidature.',
        readTime: 6,
        tags: ['lettre de motivation', 'candidature', 'rédaction', 'conseils']
      },
      {
        title: 'Préparer son entretien de bourse',
        content: `# Préparer son entretien de bourse

## Types d'entretiens
- Entretien en présentiel
- Entretien par visioconférence
- Entretien téléphonique
- Entretien de groupe

## Préparation en amont

### Recherche sur l'organisme
- Histoire et mission
- Valeurs et objectifs
- Anciens boursiers célèbres
- Actualités récentes

### Préparation des réponses
Questions fréquentes :
- Pourquoi cette bourse ?
- Quels sont vos objectifs ?
- Comment comptez-vous contribuer ?
- Parlez-nous de vous
- Vos forces et faiblesses

### Préparation matérielle
- Tenue professionnelle
- Documents à portée de main
- Test de la connexion internet (visio)
- Environnement calme

## Pendant l'entretien

### Attitude
- Soyez ponctuel
- Maintenez le contact visuel
- Écoutez attentivement
- Posez des questions pertinentes

### Communication
- Parlez clairement
- Soyez concis mais complet
- Utilisez des exemples concrets
- Montrez votre enthousiasme

## Après l'entretien
- Envoyez un email de remerciement
- Réitérez votre intérêt
- Restez patient pour la réponse

## Conseils spécifiques par type d'entretien

### Visioconférence
- Testez votre matériel
- Soignez votre arrière-plan
- Regardez la caméra, pas l'écran
- Ayez une connexion stable

### Entretien de groupe
- Respectez les autres candidats
- Participez sans monopoliser
- Montrez vos qualités de leader
- Écoutez activement`,
        category: 'preparation',
        slug: 'preparer-entretien-bourse',
        excerpt: 'Tous nos conseils pour réussir votre entretien de bourse et faire bonne impression.',
        readTime: 10,
        tags: ['entretien', 'préparation', 'conseils', 'communication']
      }
    ];
    
    for (const guide of sampleGuides) {
      await pool.query(`
        INSERT INTO guides (title, content, category, slug, excerpt, read_time, tags, is_published)
        VALUES ($1, $2, $3, $4, $5, $6, $7, true)
      `, [
        guide.title,
        guide.content,
        guide.category,
        guide.slug,
        guide.excerpt,
        guide.readTime,
        JSON.stringify(guide.tags)
      ]);
    }
    
    console.log(`✅ Inserted ${sampleGuides.length} sample guides`);
    
  } catch (error) {
    console.error('❌ Error creating guides table:', error);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting guides table creation...');
    
    // Test database connection
    await pool.query('SELECT NOW()');
    console.log('✅ Database connection successful');
    
    // Create guides table
    await createGuidesTable();
    
    console.log('🎉 Guides table setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
