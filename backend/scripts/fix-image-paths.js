/**
 * Image Path Migration Script
 * 
 * This script fixes the image path inconsistencies between database and file system
 * by mapping existing files to database records and updating the paths.
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'mabourse',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

// Directories to scan
const UPLOADS_DIR = path.join(__dirname, '../uploads');
const SCHOLARSHIPS_DIR = path.join(UPLOADS_DIR, 'scholarships');
const OPPORTUNITIES_DIR = path.join(UPLOADS_DIR, 'opportunities');

/**
 * Get all files in a directory
 */
function getFilesInDirectory(dir) {
  try {
    return fs.readdirSync(dir).filter(file => {
      const filePath = path.join(dir, file);
      return fs.statSync(filePath).isFile() && /\.(jpg|jpeg|png|gif|webp)$/i.test(file);
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    return [];
  }
}

/**
 * Create a mapping of scholarship titles to image files
 */
function createScholarshipMapping(files) {
  const mapping = {};
  
  // Common scholarship name patterns
  const patterns = [
    { keywords: ['mext', 'japan'], file: files.find(f => f.toLowerCase().includes('mext') || f.toLowerCase().includes('japan')) },
    { keywords: ['vanier', 'canada'], file: files.find(f => f.toLowerCase().includes('vanier') || f.toLowerCase().includes('canada')) },
    { keywords: ['australia', 'awards'], file: files.find(f => f.toLowerCase().includes('australia')) },
    { keywords: ['eiffel', 'france'], file: files.find(f => f.toLowerCase().includes('eiffel') || f.toLowerCase().includes('france')) },
    { keywords: ['erasmus', 'europe'], file: files.find(f => f.toLowerCase().includes('erasmus')) },
    { keywords: ['daad', 'germany', 'allemagne'], file: files.find(f => f.toLowerCase().includes('daad')) },
    { keywords: ['chevening', 'uk', 'royaume'], file: files.find(f => f.toLowerCase().includes('chevening')) },
    { keywords: ['fulbright', 'usa', 'etats'], file: files.find(f => f.toLowerCase().includes('fulbright')) },
  ];
  
  patterns.forEach(pattern => {
    if (pattern.file) {
      pattern.keywords.forEach(keyword => {
        mapping[keyword] = pattern.file;
      });
    }
  });
  
  return mapping;
}

/**
 * Update scholarship image paths in database
 */
async function updateScholarshipImages() {
  console.log('🔍 Scanning scholarship images...');
  
  const files = getFilesInDirectory(SCHOLARSHIPS_DIR);
  console.log(`Found ${files.length} image files in scholarships directory`);
  
  if (files.length === 0) {
    console.log('❌ No image files found in scholarships directory');
    return;
  }
  
  // Get all scholarships from database
  const result = await pool.query('SELECT id, title, thumbnail FROM scholarships ORDER BY id');
  const scholarships = result.rows;
  
  console.log(`Found ${scholarships.length} scholarships in database`);
  
  const mapping = createScholarshipMapping(files);
  let updatedCount = 0;
  
  for (const scholarship of scholarships) {
    let newImagePath = null;
    
    // Try to find matching image based on title keywords
    const titleLower = scholarship.title.toLowerCase();
    
    for (const [keyword, filename] of Object.entries(mapping)) {
      if (titleLower.includes(keyword)) {
        newImagePath = `/uploads/scholarships/${filename}`;
        break;
      }
    }
    
    // If no specific match, assign the first available image
    if (!newImagePath && files.length > updatedCount) {
      newImagePath = `/uploads/scholarships/${files[updatedCount % files.length]}`;
    }
    
    if (newImagePath && newImagePath !== scholarship.thumbnail) {
      await pool.query(
        'UPDATE scholarships SET thumbnail = $1, updated_at = NOW() WHERE id = $2',
        [newImagePath, scholarship.id]
      );
      
      console.log(`✅ Updated scholarship "${scholarship.title}" image: ${newImagePath}`);
      updatedCount++;
    }
  }
  
  console.log(`🎉 Updated ${updatedCount} scholarship images`);
}

/**
 * Update opportunity image paths in database
 */
async function updateOpportunityImages() {
  console.log('🔍 Scanning opportunity images...');
  
  const files = getFilesInDirectory(OPPORTUNITIES_DIR);
  console.log(`Found ${files.length} image files in opportunities directory`);
  
  if (files.length === 0) {
    console.log('❌ No image files found in opportunities directory');
    return;
  }
  
  // Get all opportunities from database
  const result = await pool.query('SELECT id, title, thumbnail, type FROM opportunities ORDER BY id');
  const opportunities = result.rows;
  
  console.log(`Found ${opportunities.length} opportunities in database`);
  
  let updatedCount = 0;
  
  for (const opportunity of opportunities) {
    let newImagePath = null;
    
    // Try to match by type first
    const typeFile = files.find(f => f.toLowerCase().includes(opportunity.type?.toLowerCase() || ''));
    if (typeFile) {
      newImagePath = `/uploads/opportunities/${typeFile}`;
    } else if (files.length > updatedCount) {
      // Assign available images in order
      newImagePath = `/uploads/opportunities/${files[updatedCount % files.length]}`;
    }
    
    if (newImagePath && newImagePath !== opportunity.thumbnail) {
      await pool.query(
        'UPDATE opportunities SET thumbnail = $1, updated_at = NOW() WHERE id = $2',
        [newImagePath, opportunity.id]
      );
      
      console.log(`✅ Updated opportunity "${opportunity.title}" image: ${newImagePath}`);
      updatedCount++;
    }
  }
  
  console.log(`🎉 Updated ${updatedCount} opportunity images`);
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting image path migration...');
    console.log('📁 Uploads directory:', UPLOADS_DIR);
    
    // Test database connection
    await pool.query('SELECT NOW()');
    console.log('✅ Database connection successful');
    
    // Update scholarship images
    await updateScholarshipImages();
    
    // Update opportunity images
    await updateOpportunityImages();
    
    console.log('🎉 Image path migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = { main };
