/**
 * Thumbnail Generation Script
 * 
 * This script generates thumbnails for all existing images in the uploads directory
 * to ensure consistent image serving across the application.
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Configuration
const UPLOADS_DIR = path.join(__dirname, '../uploads');
const SCHOLARSHIPS_DIR = path.join(UPLOADS_DIR, 'scholarships');
const OPPORTUNITIES_DIR = path.join(UPLOADS_DIR, 'opportunities');
const THUMBNAILS_DIR = path.join(UPLOADS_DIR, 'thumbnails');

const THUMBNAIL_SIZES = {
  small: { width: 150, height: 150 },
  medium: { width: 300, height: 300 },
  large: { width: 600, height: 400 },
  card: { width: 400, height: 225 }
};

const SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'webp'];

/**
 * Ensure directory exists
 */
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
}

/**
 * Get all image files in a directory
 */
function getImageFiles(dir) {
  try {
    return fs.readdirSync(dir).filter(file => {
      const ext = path.extname(file).toLowerCase().slice(1);
      return SUPPORTED_FORMATS.includes(ext) && fs.statSync(path.join(dir, file)).isFile();
    });
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    return [];
  }
}

/**
 * Generate thumbnails for a single image
 */
async function generateThumbnailsForImage(imagePath, category) {
  const filename = path.basename(imagePath, path.extname(imagePath));
  const thumbnailCategoryDir = path.join(THUMBNAILS_DIR, category);
  
  ensureDirectoryExists(thumbnailCategoryDir);
  
  const generatedThumbnails = {};
  
  for (const [sizeName, dimensions] of Object.entries(THUMBNAIL_SIZES)) {
    try {
      const thumbnailFilename = `${filename}_${sizeName}.webp`;
      const thumbnailPath = path.join(thumbnailCategoryDir, thumbnailFilename);
      
      // Skip if thumbnail already exists and is newer than original
      if (fs.existsSync(thumbnailPath)) {
        const originalStat = fs.statSync(imagePath);
        const thumbnailStat = fs.statSync(thumbnailPath);
        
        if (thumbnailStat.mtime > originalStat.mtime) {
          generatedThumbnails[sizeName] = `/uploads/thumbnails/${category}/${thumbnailFilename}`;
          continue;
        }
      }
      
      // Generate thumbnail
      await sharp(imagePath)
        .resize(dimensions.width, dimensions.height, {
          fit: 'cover',
          position: 'center',
        })
        .webp({ quality: 80 })
        .toFile(thumbnailPath);
      
      generatedThumbnails[sizeName] = `/uploads/thumbnails/${category}/${thumbnailFilename}`;
      console.log(`✅ Generated ${sizeName} thumbnail for ${filename}`);
      
    } catch (error) {
      console.error(`❌ Failed to generate ${sizeName} thumbnail for ${filename}:`, error.message);
    }
  }
  
  return generatedThumbnails;
}

/**
 * Process all images in a category
 */
async function processCategory(categoryDir, categoryName) {
  console.log(`\n🔍 Processing ${categoryName} images...`);
  
  const imageFiles = getImageFiles(categoryDir);
  console.log(`Found ${imageFiles.length} images in ${categoryName}`);
  
  if (imageFiles.length === 0) {
    console.log(`No images found in ${categoryName}`);
    return;
  }
  
  let processedCount = 0;
  let errorCount = 0;
  
  for (const imageFile of imageFiles) {
    try {
      const imagePath = path.join(categoryDir, imageFile);
      const thumbnails = await generateThumbnailsForImage(imagePath, categoryName);
      
      if (Object.keys(thumbnails).length > 0) {
        processedCount++;
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${imageFile}:`, error.message);
      errorCount++;
    }
  }
  
  console.log(`🎉 Processed ${processedCount} images in ${categoryName}`);
  if (errorCount > 0) {
    console.log(`⚠️  ${errorCount} errors occurred in ${categoryName}`);
  }
}

/**
 * Clean up old thumbnails that no longer have corresponding original images
 */
async function cleanupOrphanedThumbnails() {
  console.log('\n🧹 Cleaning up orphaned thumbnails...');
  
  const categories = ['scholarships', 'opportunities'];
  let cleanedCount = 0;
  
  for (const category of categories) {
    const categoryDir = category === 'scholarships' ? SCHOLARSHIPS_DIR : OPPORTUNITIES_DIR;
    const thumbnailCategoryDir = path.join(THUMBNAILS_DIR, category);
    
    if (!fs.existsSync(thumbnailCategoryDir)) {
      continue;
    }
    
    const originalFiles = getImageFiles(categoryDir);
    const originalBasenames = originalFiles.map(file => 
      path.basename(file, path.extname(file))
    );
    
    const thumbnailFiles = fs.readdirSync(thumbnailCategoryDir).filter(file => 
      file.endsWith('.webp')
    );
    
    for (const thumbnailFile of thumbnailFiles) {
      // Extract original filename from thumbnail name (remove _size.webp)
      const match = thumbnailFile.match(/^(.+)_(small|medium|large|card)\.webp$/);
      if (match) {
        const originalBasename = match[1];
        
        if (!originalBasenames.includes(originalBasename)) {
          const thumbnailPath = path.join(thumbnailCategoryDir, thumbnailFile);
          fs.unlinkSync(thumbnailPath);
          console.log(`🗑️  Removed orphaned thumbnail: ${thumbnailFile}`);
          cleanedCount++;
        }
      }
    }
  }
  
  console.log(`🎉 Cleaned up ${cleanedCount} orphaned thumbnails`);
}

/**
 * Main execution function
 */
async function main() {
  try {
    console.log('🚀 Starting thumbnail generation...');
    console.log(`📁 Uploads directory: ${UPLOADS_DIR}`);
    
    // Ensure thumbnail directories exist
    ensureDirectoryExists(THUMBNAILS_DIR);
    ensureDirectoryExists(path.join(THUMBNAILS_DIR, 'scholarships'));
    ensureDirectoryExists(path.join(THUMBNAILS_DIR, 'opportunities'));
    
    // Process scholarships
    await processCategory(SCHOLARSHIPS_DIR, 'scholarships');
    
    // Process opportunities
    await processCategory(OPPORTUNITIES_DIR, 'opportunities');
    
    // Clean up orphaned thumbnails
    await cleanupOrphanedThumbnails();
    
    console.log('\n🎉 Thumbnail generation completed successfully!');
    
  } catch (error) {
    console.error('❌ Thumbnail generation failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main };
