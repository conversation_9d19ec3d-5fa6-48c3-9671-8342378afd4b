/**
 * Opportunity Controller
 * 
 * Handles all opportunity-related operations with industry-standard practices
 */

import { Request, Response } from 'express';
import { Opportunity, OpportunityData } from '../models/Opportunity';
import { sendSuccess, sendError } from '../utils/response.util';
import { query } from '../config/database';
import { validateOpportunityData } from '../utils/validation.util';

/**
 * Get all opportunities with filtering and pagination
 */
export const getAllOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      limit = 10,
      type,
      location,
      isRemote,
      active,
      orderBy = 'created_at',
      orderDirection = 'DESC'
    } = req.query;

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      limit: Number(limit),
      offset,
      type: type as string,
      location: location as string,
      isRemote: isRemote === 'true' ? true : isRemote === 'false' ? false : undefined,
      isActive: active === 'true' ? true : active === 'false' ? false : undefined,
      orderBy: orderBy as 'created_at' | 'deadline' | 'title' | 'start_date',
      orderDirection: orderDirection as 'ASC' | 'DESC'
    };

    const opportunities = await Opportunity.findAll(options);
    
    sendSuccess(res, 'Opportunities retrieved successfully', opportunities);
  } catch (error) {
    console.error('Error fetching opportunities:', error);
    sendError(res, 'Failed to fetch opportunities', error);
  }
};

/**
 * Get active opportunities (not expired)
 */
export const getActiveOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const { limit } = req.query;
    
    const opportunities = await Opportunity.findActive(limit ? Number(limit) : undefined);
    
    sendSuccess(res, 'Active opportunities retrieved successfully', opportunities);
  } catch (error) {
    console.error('Error fetching active opportunities:', error);
    sendError(res, 'Failed to fetch active opportunities', error);
  }
};

/**
 * Get opportunity by ID
 */
export const getOpportunityById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid opportunity ID', null, 400);
      return;
    }

    const opportunity = await Opportunity.findById(Number(id));

    if (!opportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    sendSuccess(res, 'Opportunity retrieved successfully', opportunity);
  } catch (error) {
    console.error('Error fetching opportunity:', error);
    sendError(res, 'Failed to fetch opportunity', error);
  }
};

/**
 * Get opportunity by slug
 */
export const getOpportunityBySlug = async (req: Request, res: Response): Promise<void> => {
  try {
    const { slug } = req.params;

    if (!slug) {
      sendError(res, 'Slug is required', null, 400);
      return;
    }

    const opportunity = await Opportunity.findBySlug(slug);

    if (!opportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    sendSuccess(res, 'Opportunity retrieved successfully', opportunity);
  } catch (error) {
    console.error('Error getting opportunity by slug:', error);
    sendError(res, 'Failed to retrieve opportunity', error);
  }
};

/**
 * Get opportunities by type with pagination
 */
export const getOpportunitiesByType = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type } = req.params;
    const { page = 1, limit = 10 } = req.query;

    if (!type) {
      sendError(res, 'Type is required', null, 400);
      return;
    }

    const validTypes = ['internship', 'training', 'conference', 'workshop', 'competition'];
    if (!validTypes.includes(type)) {
      sendError(res, 'Invalid opportunity type', null, 400);
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);

    const options = {
      type,
      isActive: true,
      limit: Number(limit),
      offset,
      orderBy: 'deadline' as const,
      orderDirection: 'ASC' as const
    };

    const opportunities = await Opportunity.findAll(options);

    // Get total count for pagination
    const countResult = await query('SELECT COUNT(*) as total FROM opportunities WHERE type = $1 AND is_active = true', [type]);
    const total = parseInt(countResult.rows[0].total);

    const pagination = {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages: Math.ceil(total / Number(limit)),
      hasNextPage: Number(page) < Math.ceil(total / Number(limit)),
      hasPreviousPage: Number(page) > 1
    };

    sendSuccess(res, 'Opportunities retrieved successfully', { data: opportunities, pagination });
  } catch (error) {
    console.error('Error fetching opportunities by type:', error);
    sendError(res, 'Failed to fetch opportunities', error);
  }
};

/**
 * Search opportunities
 */
export const searchOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const { q, type, location, isRemote, page = 1, limit = 10 } = req.query;
    
    if (!q || typeof q !== 'string') {
      sendError(res, 'Search query is required', null, 400);
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);
    
    const options = {
      type: type as string,
      location: location as string,
      isRemote: isRemote === 'true' ? true : isRemote === 'false' ? false : undefined,
      limit: Number(limit),
      offset
    };

    const opportunities = await Opportunity.search(q, options);
    
    sendSuccess(res, 'Search completed successfully', {
      opportunities,
      query: q,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: opportunities.length
      }
    });
  } catch (error) {
    console.error('Error searching opportunities:', error);
    sendError(res, 'Failed to search opportunities', error);
  }
};

/**
 * Create new opportunity (Admin only)
 */
export const createOpportunity = async (req: Request, res: Response): Promise<void> => {
  try {
    const opportunityData: Omit<OpportunityData, 'id' | 'createdAt' | 'updatedAt'> = req.body;
    
    // Validate opportunity data
    const validation = validateOpportunityData(opportunityData);
    if (!validation.isValid) {
      sendError(res, 'Validation failed', validation.errors, 400);
      return;
    }

    // Add admin info
    opportunityData.createdByAdmin = (req as any).admin?.id || null;

    // Ensure deadline is a valid Date
    if (opportunityData.deadline && typeof opportunityData.deadline === 'string') {
      try {
        const date = new Date(opportunityData.deadline);
        if (!isNaN(date.getTime())) {
          opportunityData.deadline = date;
        }
      } catch (err) {
        sendError(res, 'Invalid deadline date format', null, 400);
        return;
      }
    }

    const opportunity = await Opportunity.create(opportunityData);
    
    sendSuccess(res, 'Opportunity created successfully', opportunity, 201);
  } catch (error) {
    console.error('Error creating opportunity:', error);
    sendError(res, 'Failed to create opportunity', error);
  }
};

/**
 * Update opportunity (Admin only)
 */
export const updateOpportunity = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const updateData: Partial<OpportunityData> = req.body;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid opportunity ID', null, 400);
      return;
    }

    // Check if opportunity exists
    const existingOpportunity = await Opportunity.findById(Number(id));
    if (!existingOpportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    // Validate update data
    const validation = validateOpportunityData(updateData, true);
    if (!validation.isValid) {
      sendError(res, 'Validation failed', validation.errors, 400);
      return;
    }

    // Ensure deadline is a valid Date if provided
    if (updateData.deadline && typeof updateData.deadline === 'string') {
      try {
        const date = new Date(updateData.deadline);
        if (!isNaN(date.getTime())) {
          updateData.deadline = date;
        } else {
          sendError(res, 'Invalid deadline date format', null, 400);
          return;
        }
      } catch (err) {
        sendError(res, 'Invalid deadline date format', null, 400);
        return;
      }
    }

    const opportunity = await Opportunity.update(Number(id), updateData);
    
    if (!opportunity) {
      sendError(res, 'Failed to update opportunity', null, 500);
      return;
    }

    sendSuccess(res, 'Opportunity updated successfully', opportunity);
  } catch (error) {
    console.error('Error updating opportunity:', error);
    sendError(res, 'Failed to update opportunity', error);
  }
};

/**
 * Delete opportunity (Admin only)
 */
export const deleteOpportunity = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    if (!id || isNaN(Number(id))) {
      sendError(res, 'Invalid opportunity ID', null, 400);
      return;
    }

    // Check if opportunity exists
    const existingOpportunity = await Opportunity.findById(Number(id));
    if (!existingOpportunity) {
      sendError(res, 'Opportunity not found', null, 404);
      return;
    }

    const deleted = await Opportunity.delete(Number(id));
    
    if (!deleted) {
      sendError(res, 'Failed to delete opportunity', null, 500);
      return;
    }

    sendSuccess(res, 'Opportunity deleted successfully', null);
  } catch (error) {
    console.error('Error deleting opportunity:', error);
    sendError(res, 'Failed to delete opportunity', error);
  }
};

/**
 * Get opportunity types for dropdown menu
 */
export const getOpportunityTypes = async (req: Request, res: Response): Promise<void> => {
  try {
    const result = await query(`
      SELECT
        type,
        COUNT(*) as count,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_count
      FROM opportunities
      WHERE type IS NOT NULL AND type != ''
      GROUP BY type
      ORDER BY count DESC
    `);

    const types = result.rows.map(row => ({
      name: row.type,
      count: parseInt(row.count),
      activeCount: parseInt(row.active_count),
      slug: row.type.toLowerCase().replace(/\s+/g, '-')
    }));

    sendSuccess(res, 'Opportunity types retrieved successfully', types);
  } catch (error) {
    console.error('Error fetching opportunity types:', error);
    sendError(res, 'Failed to fetch opportunity types', error);
  }
};

/**
 * Get latest opportunities for sidebar - Industry Standard Implementation
 */
export const getLatestOpportunities = async (req: Request, res: Response): Promise<void> => {
  try {
    const { limit = '6', excludeId } = req.query;
    const limitNumber = Math.min(parseInt(limit as string, 10), 20);

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // Exclude specific opportunity if provided
    if (excludeId) {
      whereClause += ' AND id != $1';
      params.push(parseInt(excludeId as string, 10));
    }

    const result = await query(`
      SELECT
        id, title, type, organization, location, deadline, is_active,
        thumbnail, created_at, start_date, end_date
      FROM opportunities
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);

    const opportunities = result.rows.map(row => ({
      id: row.id,
      title: row.title,
      type: row.type,
      organization: row.organization,
      location: row.location,
      deadline: row.deadline,
      isActive: row.is_active,
      thumbnail: row.thumbnail,
      createdAt: row.created_at,
      startDate: row.start_date,
      endDate: row.end_date
    }));

    return sendSuccess(res, 'Latest opportunities retrieved successfully', opportunities);
  } catch (error) {
    console.error('Error fetching latest opportunities:', error);
    return sendError(res, 'Failed to fetch latest opportunities', error);
  }
};

/**
 * Get opportunity types for sidebar - Industry Standard Implementation
 */
export const getOpportunityTypesForSidebar = async (req: Request, res: Response): Promise<void> => {
  try {
    const { limit = '10', excludeType } = req.query;
    const limitNumber = Math.min(parseInt(limit as string, 10), 20);

    let whereClause = 'WHERE type IS NOT NULL AND type != \'\'';
    const params: any[] = [];

    if (excludeType) {
      whereClause += ' AND type != $1';
      params.push(decodeURIComponent(excludeType as string));
    }

    const result = await query(`
      SELECT
        type,
        COUNT(*) as total_count,
        COUNT(CASE WHEN is_active = true THEN 1 END) as active_count,
        MAX(created_at) as latest_opportunity
      FROM opportunities
      ${whereClause}
      GROUP BY type
      ORDER BY total_count DESC, latest_opportunity DESC
      LIMIT $${params.length + 1}
    `, [...params, limitNumber]);

    const types = result.rows.map(row => ({
      name: row.type,
      totalCount: parseInt(row.total_count),
      activeCount: parseInt(row.active_count),
      latestOpportunity: row.latest_opportunity,
      slug: row.type.toLowerCase().replace(/\s+/g, '-')
    }));

    return sendSuccess(res, 'Opportunity types for sidebar retrieved successfully', types);
  } catch (error) {
    console.error('Error fetching opportunity types for sidebar:', error);
    return sendError(res, 'Failed to fetch opportunity types for sidebar', error);
  }
};
