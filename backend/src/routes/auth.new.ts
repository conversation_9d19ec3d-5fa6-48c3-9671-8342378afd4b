import express from 'express';
import { body } from 'express-validator';
import {
  adminLogin,
  adminLogout,
  getCurrentAdmin,
  verifyAuth,
  changePassword,
  requestPasswordReset,
  resetPassword
} from '../controllers/auth.controller.new';
import { authLimiter as loginLimiter } from '../middleware/rateLimiting.middleware';
import { 
  authenticate, 
  requireAdmin, 
  requireMainAdmin 
} from '../middleware/auth.new';

const router = express.Router();

/**
 * @route POST /api/auth/admin/login
 * @desc Admin login
 * @access Public
 */
router.post(
  '/admin/login',
  loginLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please enter a valid email address'),
    body('password')
      .isLength({ min: 1 })
      .withMessage('Password is required'),
  ],
  adminLogin
);

/**
 * @route POST /api/auth/admin/logout
 * @desc Admin logout
 * @access Private
 */
router.post('/admin/logout', authenticate, requireAdmin, adminLogout);

/**
 * @route GET /api/auth/admin/profile
 * @desc Get current admin profile
 * @access Private (Admin only)
 */
router.get('/admin/profile', authenticate, requireAdmin, getCurrentAdmin);

/**
 * @route GET /api/auth/admin/me
 * @desc Get current admin profile (alias for frontend)
 * @access Private (Admin only)
 */
router.get('/admin/me', authenticate, requireAdmin, getCurrentAdmin);

/**
 * @route POST /api/auth/admin/change-password
 * @desc Change admin password
 * @access Private (Admin only)
 */
router.post(
  '/admin/change-password',
  requireAdmin,
  [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 12 })
      .withMessage('New password must be at least 12 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/)
      .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  ],
  changePassword
);

/**
 * @route GET /api/auth/debug
 * @desc Debug authentication state
 * @access Public
 */
router.get('/debug', (req, res) => {
  const token = req.cookies.auth_token;
  res.json({
    success: true,
    debug: {
      hasCookie: !!token,
      cookieValue: token ? 'present' : 'missing',
      cookies: Object.keys(req.cookies),
      headers: {
        authorization: req.headers.authorization,
        cookie: req.headers.cookie
      }
    }
  });
});

/**
 * @route POST /api/auth/clear-cookies
 * @desc Clear all authentication cookies (for debugging)
 * @access Public
 */
router.post('/clear-cookies', (req, res) => {
  // Clear all possible authentication cookies
  res.clearCookie('auth_token', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/'
  });

  // Clear any other potential cookies
  res.clearCookie('token');
  res.clearCookie('admin_token');
  res.clearCookie('session');

  res.json({
    success: true,
    message: 'All authentication cookies cleared'
  });
});

/**
 * @route POST /api/auth/reset-admin-account
 * @desc Reset admin account (clear failed attempts and unlock)
 * @access Public (for debugging only)
 */
router.post('/reset-admin-account', async (req, res) => {
  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required'
      });
    }

    // Import Admin model
    const { Admin } = await import('../models/Admin');

    // Find admin by email first
    const admin = await Admin.findByEmail(email);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: 'Admin not found'
      });
    }

    // Reset failed login attempts and unlock account
    await Admin.updateFailedLoginAttempts(
      admin.id!,
      0,
      undefined // Clear lock_until
    );

    res.json({
      success: true,
      message: 'Admin account reset successfully',
      data: {
        email,
        id: admin.id,
        previousAttempts: admin.failedLoginAttempts,
        wasLocked: !!admin.lockUntil,
        reset: true
      }
    });

  } catch (error) {
    console.error('Reset admin account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset admin account',
      error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined
    });
  }
});

/**
 * @route POST /api/auth/reset-rate-limit
 * @desc Reset rate limit for testing purposes (development only)
 * @access Public (development only)
 */
router.post('/reset-rate-limit', (req, res) => {
  // Only allow in development environment
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({
      success: false,
      message: 'Rate limit reset not available in production'
    });
  }

  try {
    // Get the IP address using the same logic as the rate limiter
    const ip = req.ip || (req as any).connection?.remoteAddress || 'unknown';

    // Access the rate limiter's store directly
    const store = (loginLimiter as any).store;

    // Try multiple methods to reset the rate limit
    let resetSuccess = false;

    if (store && typeof store.resetKey === 'function') {
      // Method 1: Use resetKey if available
      store.resetKey(ip);
      resetSuccess = true;
      console.log(`Rate limit reset using resetKey for IP: ${ip}`);
    } else if (store && typeof store.delete === 'function') {
      // Method 2: Use delete method
      store.delete(ip);
      resetSuccess = true;
      console.log(`Rate limit cleared using delete for IP: ${ip}`);
    } else if (store && store.hits) {
      // Method 3: Direct manipulation of hits map (MemoryStore)
      delete store.hits[ip];
      resetSuccess = true;
      console.log(`Rate limit manually cleared from hits map for IP: ${ip}`);
    } else if (store && store.data) {
      // Method 4: Try data property (some store implementations)
      delete store.data[ip];
      resetSuccess = true;
      console.log(`Rate limit cleared from data property for IP: ${ip}`);
    }

    // Additional cleanup - try to clear from any possible storage locations
    if (store) {
      // Clear from all possible property names
      const possibleProps = ['hits', 'data', 'cache', 'store', 'records'];
      for (const prop of possibleProps) {
        if (store[prop] && typeof store[prop] === 'object') {
          delete store[prop][ip];
        }
      }
    }

    res.json({
      success: true,
      message: 'Rate limit reset successfully',
      ip: ip,
      note: 'This endpoint is only available in development mode',
      debug: {
        storeType: store ? store.constructor.name : 'unknown',
        hasResetKey: store && typeof store.resetKey === 'function',
        hasDelete: store && typeof store.delete === 'function',
        hasHits: store && !!store.hits,
        resetSuccess
      }
    });
  } catch (error) {
    console.error('Rate limit reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset rate limit',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @route GET /api/auth/verify
 * @desc Verify authentication status
 * @access Private
 */
router.get('/verify', authenticate, verifyAuth);

/**
 * @route GET /api/auth/admin/verify
 * @desc Verify admin authentication status
 * @access Private (Admin only)
 */
router.get('/admin/verify', requireAdmin, verifyAuth);

/**
 * @route GET /api/auth/admin/profile
 * @desc Get current admin profile
 * @access Private (Admin only)
 */
router.get('/admin/profile', authenticate, requireAdmin, getCurrentAdmin);

/**
 * @route POST /api/auth/admin/request-password-reset
 * @desc Request password reset
 * @access Public
 */
router.post(
  '/admin/request-password-reset',
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address')
  ],
  requestPasswordReset
);

/**
 * @route POST /api/auth/admin/reset-password
 * @desc Reset password with token
 * @access Public
 */
router.post(
  '/admin/reset-password',
  [
    body('token')
      .notEmpty()
      .withMessage('Reset token is required'),
    body('newPassword')
      .isLength({ min: 12 })
      .withMessage('Password must be at least 12 characters long')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain uppercase, lowercase, number and special character')
  ],
  resetPassword
);

export default router;
