import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { Newsletter } from '../models/Newsletter';
import { authenticate, requireAdmin } from '../middleware/auth.new';

const router = Router();

// ADMIN-ONLY ROUTES - Require authentication
// Get all subscribers (admin only)
router.get('/', authenticate, requireAdmin, async (req: Request, res: Response) => {
  try {
    const subscribers = await Newsletter.findAll();
    res.json({
      success: true,
      data: subscribers
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Error fetching subscribers'
    });
  }
});

// Get all subscribers (alias for admin portal) - admin only
router.get('/subscribers', authenticate, requireAdmin, async (req: Request, res: Response) => {
  try {
    const subscribers = await Newsletter.findAll();
    res.json({
      success: true,
      data: subscribers
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Error fetching subscribers'
    });
  }
});

// Add a new subscriber
router.post('/', async (req: Request, res: Response) => {
  try {
    const { email } = req.body;
    const subscriber = await Newsletter.create(email);
    res.json(subscriber);
  } catch (error) {
    res.status(500).json({ error: 'Error adding subscriber' });
  }
});

// Delete a subscriber
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await Newsletter.delete(Number(id));
    res.json({ message: 'Subscriber removed successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Error removing subscriber' });
  }
});

// Bulk import subscribers
router.post('/bulk', [
  body('emails').isArray({ min: 1 }).withMessage('At least one email is required'),
  body('emails.*').isEmail().withMessage('Invalid email format')
], async (req: Request, res: Response) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { emails } = req.body;
    console.log(`Attempting to bulk import ${emails.length} subscribers`);

    // Track results
    const results: {
      success: number;
      duplicates: number;
      failures: number;
      details: Array<{ email: string; status: string }>;
    } = {
      success: 0,
      duplicates: 0,
      failures: 0,
      details: []
    };

    // Process each email
    for (const email of emails) {
      try {
        // Check if email already exists
        const existing = await Newsletter.findByEmail(email);

        if (existing) {
          results.duplicates++;
          results.details.push({ email, status: 'duplicate' });
          continue;
        }

        // Create new subscriber
        await Newsletter.create(email);

        results.success++;
        results.details.push({ email, status: 'success' });
      } catch (error) {
        console.error(`Error adding subscriber ${email}:`, error);
        results.failures++;
        results.details.push({ email, status: 'error' });
      }
    }

    console.log(`Bulk import results: ${results.success} added, ${results.duplicates} duplicates, ${results.failures} failures`);
    res.json({
      message: `Successfully imported ${results.success} subscribers. ${results.duplicates} duplicates skipped. ${results.failures} failures.`,
      results
    });
  } catch (error) {
    console.error('Bulk import error:', error);
    res.status(500).json({ error: 'Error processing bulk import' });
  }
});

export default router;