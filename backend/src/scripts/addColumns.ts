/**
 * <PERSON><PERSON><PERSON> to add the new columns to opportunities table
 */

import { query, initializeDatabase } from '../config/database';

async function addColumns() {
  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('🚀 Adding new columns to opportunities table...');
    
    // Add the new columns
    await query(`
      ALTER TABLE opportunities 
      ADD COLUMN IF NOT EXISTS eligibility_criteria TEXT,
      ADD COLUMN IF NOT EXISTS required_documents TEXT,
      ADD COLUMN IF NOT EXISTS how_to_apply TEXT;
    `);
    
    console.log('✅ Columns added successfully!');
    
    // Verify the columns exist
    const result = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'opportunities' 
      AND column_name IN ('eligibility_criteria', 'required_documents', 'how_to_apply');
    `);
    
    console.log('📋 New columns found:', result.rows.map(row => row.column_name));
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error adding columns:', error);
    process.exit(1);
  }
}

// Run the script
addColumns();
