/**
 * CHECK ADMIN STATUS SCRIPT
 * Simple script to check admin account status and reset if needed
 */

import { query, initializeDatabase } from '../config/database';

async function checkAdminStatus(): Promise<void> {
  console.log('🔍 CHECKING ADMIN STATUS');
  console.log('=' .repeat(50));

  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('✅ Database connection established');

    // Check admin account
    const adminResult = await query('SELECT * FROM admins WHERE email = $1', ['<EMAIL>']);
    
    if (adminResult.rows.length === 0) {
      console.log('❌ No admin found <NAME_EMAIL>');
      console.log('📋 Available admins:');
      const allAdmins = await query('SELECT id, email, is_main_admin, failed_login_attempts, lock_until FROM admins');
      console.table(allAdmins.rows);
    } else {
      const admin = adminResult.rows[0];
      console.log('📊 Admin account found:');
      console.log(`   - ID: ${admin.id}`);
      console.log(`   - Email: ${admin.email}`);
      console.log(`   - Failed attempts: ${admin.failed_login_attempts}`);
      console.log(`   - Locked until: ${admin.lock_until || 'Not locked'}`);
      console.log(`   - Is main admin: ${admin.is_main_admin}`);
      console.log(`   - Created: ${admin.created_at}`);
      console.log(`   - Updated: ${admin.updated_at}`);

      // Reset the account
      console.log('🔧 Resetting admin account...');
      await query(`
        UPDATE admins 
        SET failed_login_attempts = 0, 
            lock_until = NULL,
            updated_at = NOW()
        WHERE email = $1
      `, ['<EMAIL>']);
      console.log('✅ Admin account reset successfully');
    }

    // Check all tables to understand the structure
    console.log('📋 Database tables:');
    const tablesResult = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('Available tables:');
    tablesResult.rows.forEach((row: any) => {
      console.log(`   - ${row.table_name}`);
    });

  } catch (error) {
    console.error('❌ Check failed:', error);
    throw error;
  }
}

// Run check if called directly
if (require.main === module) {
  checkAdminStatus()
    .then(() => {
      console.log('✅ Admin status check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Admin status check failed:', error);
      process.exit(1);
    });
}

export { checkAdminStatus };
