/**
 * Database Health and Performance Check Script
 */

import { query, initializeDatabase } from '../config/database';

interface TableInfo {
  tableName: string;
  rowCount: number;
  sizeBytes: number;
  sizeFormatted: string;
}

interface IndexInfo {
  tableName: string;
  indexName: string;
  indexSize: string;
  isUnique: boolean;
}

interface DatabaseStats {
  totalSize: string;
  totalTables: number;
  totalIndexes: number;
  connectionCount: number;
}

async function checkDatabaseHealth() {
  try {
    await initializeDatabase();
    console.log('🏥 Database Health Check Starting...\n');

    // 1. Check database size and basic stats
    console.log('📊 DATABASE OVERVIEW');
    console.log('='.repeat(50));
    
    const dbSizeResult = await query(`
      SELECT pg_size_pretty(pg_database_size(current_database())) as database_size;
    `);
    
    const tableCountResult = await query(`
      SELECT count(*) as table_count 
      FROM information_schema.tables 
      WHERE table_schema = 'public';
    `);
    
    const indexCountResult = await query(`
      SELECT count(*) as index_count 
      FROM pg_indexes 
      WHERE schemaname = 'public';
    `);
    
    const connectionCountResult = await query(`
      SELECT count(*) as connection_count 
      FROM pg_stat_activity 
      WHERE datname = current_database();
    `);

    const stats: DatabaseStats = {
      totalSize: dbSizeResult.rows[0].database_size,
      totalTables: parseInt(tableCountResult.rows[0].table_count),
      totalIndexes: parseInt(indexCountResult.rows[0].index_count),
      connectionCount: parseInt(connectionCountResult.rows[0].connection_count)
    };

    console.log(`Database Size: ${stats.totalSize}`);
    console.log(`Total Tables: ${stats.totalTables}`);
    console.log(`Total Indexes: ${stats.totalIndexes}`);
    console.log(`Active Connections: ${stats.connectionCount}`);
    console.log('');

    // 2. Check table sizes and row counts
    console.log('📋 TABLE ANALYSIS');
    console.log('='.repeat(50));
    
    const tableStatsResult = await query(`
      SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
      FROM pg_stats 
      WHERE schemaname = 'public'
      ORDER BY tablename, attname;
    `);

    const tableSizesResult = await query(`
      SELECT 
        t.table_name,
        (SELECT count(*) FROM information_schema.columns WHERE table_name = t.table_name) as column_count,
        pg_size_pretty(pg_total_relation_size(quote_ident(t.table_name)::regclass)) as size
      FROM information_schema.tables t
      WHERE t.table_schema = 'public'
      ORDER BY pg_total_relation_size(quote_ident(t.table_name)::regclass) DESC;
    `);

    tableSizesResult.rows.forEach(row => {
      console.log(`${row.table_name.padEnd(25)} | ${row.column_count.toString().padEnd(8)} columns | ${row.size}`);
    });
    console.log('');

    // 3. Check specific table row counts
    console.log('📈 ROW COUNTS');
    console.log('='.repeat(50));
    
    const tables = ['scholarships', 'opportunities', 'guides', 'users', 'admins', 'newsletter_subscribers'];
    
    for (const table of tables) {
      try {
        const result = await query(`SELECT count(*) as count FROM ${table}`);
        console.log(`${table.padEnd(25)} | ${result.rows[0].count.padStart(8)} rows`);
      } catch (error) {
        console.log(`${table.padEnd(25)} | ${'ERROR'.padStart(8)}`);
      }
    }
    console.log('');

    // 4. Check indexes
    console.log('🔍 INDEX ANALYSIS');
    console.log('='.repeat(50));
    
    const indexResult = await query(`
      SELECT 
        t.relname as table_name,
        i.relname as index_name,
        pg_size_pretty(pg_relation_size(i.oid)) as index_size,
        ix.indisunique as is_unique,
        array_to_string(array_agg(a.attname), ', ') as columns
      FROM pg_class t
      JOIN pg_index ix ON t.oid = ix.indrelid
      JOIN pg_class i ON i.oid = ix.indexrelid
      JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
      WHERE t.relkind = 'r'
        AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
      GROUP BY t.relname, i.relname, i.oid, ix.indisunique
      ORDER BY t.relname, i.relname;
    `);

    indexResult.rows.forEach(row => {
      const uniqueFlag = row.is_unique ? '[UNIQUE]' : '';
      console.log(`${row.table_name.padEnd(20)} | ${row.index_name.padEnd(30)} | ${row.index_size.padEnd(8)} | ${row.columns} ${uniqueFlag}`);
    });
    console.log('');

    // 5. Check for missing indexes (slow queries)
    console.log('⚠️  PERFORMANCE RECOMMENDATIONS');
    console.log('='.repeat(50));
    
    // Check for tables without indexes on foreign keys
    const foreignKeyResult = await query(`
      SELECT 
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public';
    `);

    console.log('Foreign Key Relationships:');
    foreignKeyResult.rows.forEach(row => {
      console.log(`${row.table_name}.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name}`);
    });
    console.log('');

    // 6. Check query performance stats
    console.log('🚀 QUERY PERFORMANCE');
    console.log('='.repeat(50));
    
    const queryStatsResult = await query(`
      SELECT 
        schemaname,
        tablename,
        seq_scan,
        seq_tup_read,
        idx_scan,
        idx_tup_fetch,
        n_tup_ins,
        n_tup_upd,
        n_tup_del
      FROM pg_stat_user_tables
      WHERE schemaname = 'public'
      ORDER BY seq_scan DESC;
    `);

    console.log('Table'.padEnd(20) + ' | ' + 'Seq Scans'.padEnd(10) + ' | ' + 'Index Scans'.padEnd(12) + ' | ' + 'Inserts'.padEnd(8) + ' | ' + 'Updates'.padEnd(8) + ' | ' + 'Deletes');
    console.log('-'.repeat(80));
    queryStatsResult.rows.forEach(row => {
      console.log(
        `${row.tablename.padEnd(20)} | ${(row.seq_scan || 0).toString().padEnd(10)} | ${(row.idx_scan || 0).toString().padEnd(12)} | ${(row.n_tup_ins || 0).toString().padEnd(8)} | ${(row.n_tup_upd || 0).toString().padEnd(8)} | ${(row.n_tup_del || 0)}`
      );
    });
    console.log('');

    // 7. Recommendations
    console.log('💡 RECOMMENDATIONS');
    console.log('='.repeat(50));
    
    let recommendations = [];
    
    // Check for tables with high sequential scans
    queryStatsResult.rows.forEach(row => {
      if (row.seq_scan > 100 && (row.idx_scan || 0) < row.seq_scan) {
        recommendations.push(`Consider adding indexes to ${row.tablename} - high sequential scan ratio`);
      }
    });
    
    // Check for large tables without proper indexing
    if (stats.totalTables > 10 && stats.totalIndexes < stats.totalTables * 2) {
      recommendations.push('Consider adding more indexes for better query performance');
    }
    
    if (stats.connectionCount > 20) {
      recommendations.push('High number of active connections - consider connection pooling optimization');
    }

    if (recommendations.length === 0) {
      console.log('✅ Database appears to be healthy with good performance characteristics');
    } else {
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }

    console.log('\n🎉 Database Health Check Complete!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error during database health check:', error);
    process.exit(1);
  }
}

// Run the health check
checkDatabaseHealth();
