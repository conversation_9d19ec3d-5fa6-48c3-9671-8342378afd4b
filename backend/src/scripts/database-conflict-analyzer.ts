/**
 * DATABASE CONFLICT ANALYZER
 * Identifies and maps all database system conflicts for nuclear elimination
 */

import fs from 'fs';
import path from 'path';
import { query, initializeDatabase } from '../config/database';

interface ConflictAnalysis {
  prismaFiles: string[];
  sequelizeFiles: string[];
  rawQueryFiles: string[];
  configFiles: string[];
  modelFiles: string[];
  migrationFiles: string[];
  duplicateControllers: string[];
  conflictingRoutes: string[];
}

async function analyzeDatabaseConflicts(): Promise<ConflictAnalysis> {
  console.log('🔍 ANALYZING DATABASE CONFLICTS - NUCLEAR PROTOCOL');
  console.log('=' .repeat(70));

  const analysis: ConflictAnalysis = {
    prismaFiles: [],
    sequelizeFiles: [],
    rawQueryFiles: [],
    configFiles: [],
    modelFiles: [],
    migrationFiles: [],
    duplicateControllers: [],
    conflictingRoutes: []
  };

  const backendDir = path.join(__dirname, '../..');

  // Scan for Prisma files
  console.log('🔍 Scanning for PRISMA conflicts...');
  const prismaFiles = await findFilesContaining(backendDir, ['prisma', '@prisma', 'PrismaClient']);
  analysis.prismaFiles = prismaFiles;
  console.log(`📊 Found ${prismaFiles.length} Prisma-related files`);

  // Scan for Sequelize files
  console.log('🔍 Scanning for SEQUELIZE conflicts...');
  const sequelizeFiles = await findFilesContaining(backendDir, ['sequelize', 'Sequelize', 'DataTypes']);
  analysis.sequelizeFiles = sequelizeFiles;
  console.log(`📊 Found ${sequelizeFiles.length} Sequelize-related files`);

  // Scan for raw query files
  console.log('🔍 Scanning for RAW QUERY usage...');
  const rawQueryFiles = await findFilesContaining(backendDir, ['query(', 'database.query', 'pool.query']);
  analysis.rawQueryFiles = rawQueryFiles;
  console.log(`📊 Found ${rawQueryFiles.length} raw query files`);

  // Find duplicate controllers
  console.log('🔍 Scanning for DUPLICATE CONTROLLERS...');
  const controllerDir = path.join(backendDir, 'src/controllers');
  if (fs.existsSync(controllerDir)) {
    const controllers = fs.readdirSync(controllerDir);
    const duplicates = controllers.filter(file => 
      controllers.some(other => 
        other !== file && 
        other.replace('.prisma', '').replace('.sequelize', '') === file.replace('.prisma', '').replace('.sequelize', '')
      )
    );
    analysis.duplicateControllers = duplicates.map(file => path.join(controllerDir, file));
  }
  console.log(`📊 Found ${analysis.duplicateControllers.length} duplicate controllers`);

  // Find conflicting routes
  console.log('🔍 Scanning for CONFLICTING ROUTES...');
  const routesDir = path.join(backendDir, 'src/routes');
  if (fs.existsSync(routesDir)) {
    const routes = fs.readdirSync(routesDir);
    const conflicts = routes.filter(file => 
      routes.some(other => 
        other !== file && 
        (other.includes('scholarship') && file.includes('scholarship') ||
         other.includes('message') && file.includes('message'))
      )
    );
    analysis.conflictingRoutes = conflicts.map(file => path.join(routesDir, file));
  }
  console.log(`📊 Found ${analysis.conflictingRoutes.length} conflicting routes`);

  // Check database tables
  console.log('🔍 Analyzing DATABASE TABLES...');
  try {
    await initializeDatabase();
    const tablesResult = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('📊 Current database tables:');
    tablesResult.rows.forEach((row: any) => {
      console.log(`   - ${row.table_name}`);
    });
  } catch (error) {
    console.log('⚠️ Database table analysis failed:', error);
  }

  // Generate conflict report
  const reportPath = path.join(backendDir, 'backups', `conflict_analysis_${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(analysis, null, 2));

  console.log('=' .repeat(70));
  console.log('📊 CONFLICT ANALYSIS COMPLETE');
  console.log(`📁 Report saved: ${reportPath}`);
  console.log('🚀 READY FOR NUCLEAR ELIMINATION');
  console.log('=' .repeat(70));

  return analysis;
}

async function findFilesContaining(dir: string, patterns: string[]): Promise<string[]> {
  const results: string[] = [];
  
  function scanDirectory(currentDir: string) {
    if (!fs.existsSync(currentDir)) return;
    
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and dist directories
        if (!item.includes('node_modules') && !item.includes('dist') && !item.includes('.git')) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.js'))) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          if (patterns.some(pattern => content.includes(pattern))) {
            results.push(fullPath);
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }
  }
  
  scanDirectory(dir);
  return results;
}

// Run analysis if called directly
if (require.main === module) {
  analyzeDatabaseConflicts()
    .then(() => {
      console.log('✅ Database conflict analysis completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Database conflict analysis failed:', error);
      process.exit(1);
    });
}

export { analyzeDatabaseConflicts };
