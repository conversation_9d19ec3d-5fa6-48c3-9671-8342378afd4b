/**
 * EMERGENCY BACKUP SCRIPT
 * Creates complete backup of current database state before nuclear remediation
 */

import { query, initializeDatabase } from '../config/database';
import fs from 'fs';
import path from 'path';

interface BackupData {
  admins: any[];
  scholarships: any[];
  opportunities: any[];
  messages: any[];
  newsletter_subscribers: any[];
  users: any[];
  guides: any[];
}

async function createEmergencyBackup(): Promise<void> {
  console.log('🚨 CREATING EMERGENCY BACKUP - NUCLEAR REMEDIATION PROTOCOL');
  console.log('=' .repeat(70));

  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('✅ Database connection established');

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, '../../backups');
    
    // Create backups directory
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const backupData: BackupData = {
      admins: [],
      scholarships: [],
      opportunities: [],
      messages: [],
      newsletter_subscribers: [],
      users: [],
      guides: []
    };

    // Backup all critical tables
    console.log('📊 Backing up ADMINS table...');
    try {
      const adminsResult = await query('SELECT * FROM admins ORDER BY id');
      backupData.admins = adminsResult.rows;
      console.log(`✅ Backed up ${adminsResult.rows.length} admin records`);
    } catch (error) {
      console.log('⚠️ Admins table backup failed:', error);
    }

    console.log('📊 Backing up SCHOLARSHIPS table...');
    try {
      const scholarshipsResult = await query('SELECT * FROM scholarships ORDER BY id');
      backupData.scholarships = scholarshipsResult.rows;
      console.log(`✅ Backed up ${scholarshipsResult.rows.length} scholarship records`);
    } catch (error) {
      console.log('⚠️ Scholarships table backup failed:', error);
    }

    console.log('📊 Backing up OPPORTUNITIES table...');
    try {
      const opportunitiesResult = await query('SELECT * FROM opportunities ORDER BY id');
      backupData.opportunities = opportunitiesResult.rows;
      console.log(`✅ Backed up ${opportunitiesResult.rows.length} opportunity records`);
    } catch (error) {
      console.log('⚠️ Opportunities table backup failed:', error);
    }

    console.log('📊 Backing up MESSAGES table...');
    try {
      const messagesResult = await query('SELECT * FROM messages ORDER BY id');
      backupData.messages = messagesResult.rows;
      console.log(`✅ Backed up ${messagesResult.rows.length} message records`);
    } catch (error) {
      console.log('⚠️ Messages table backup failed:', error);
    }

    console.log('📊 Backing up NEWSLETTER_SUBSCRIBERS table...');
    try {
      const subscribersResult = await query('SELECT * FROM newsletter_subscribers ORDER BY id');
      backupData.newsletter_subscribers = subscribersResult.rows;
      console.log(`✅ Backed up ${subscribersResult.rows.length} subscriber records`);
    } catch (error) {
      console.log('⚠️ Newsletter subscribers table backup failed:', error);
    }

    console.log('📊 Backing up USERS table...');
    try {
      const usersResult = await query('SELECT * FROM users ORDER BY id');
      backupData.users = usersResult.rows;
      console.log(`✅ Backed up ${usersResult.rows.length} user records`);
    } catch (error) {
      console.log('⚠️ Users table backup failed:', error);
    }

    console.log('📊 Backing up GUIDES table...');
    try {
      const guidesResult = await query('SELECT * FROM guides ORDER BY id');
      backupData.guides = guidesResult.rows;
      console.log(`✅ Backed up ${guidesResult.rows.length} guide records`);
    } catch (error) {
      console.log('⚠️ Guides table backup failed:', error);
    }

    // Save backup to file
    const backupFile = path.join(backupDir, `emergency_backup_${timestamp}.json`);
    fs.writeFileSync(backupFile, JSON.stringify(backupData, null, 2));

    // Create summary report
    const summaryFile = path.join(backupDir, `backup_summary_${timestamp}.txt`);
    const summary = `
EMERGENCY BACKUP SUMMARY
========================
Timestamp: ${new Date().toISOString()}
Backup File: ${backupFile}

DATA COUNTS:
- Admins: ${backupData.admins.length}
- Scholarships: ${backupData.scholarships.length}
- Opportunities: ${backupData.opportunities.length}
- Messages: ${backupData.messages.length}
- Newsletter Subscribers: ${backupData.newsletter_subscribers.length}
- Users: ${backupData.users.length}
- Guides: ${backupData.guides.length}

TOTAL RECORDS: ${Object.values(backupData).reduce((sum, arr) => sum + arr.length, 0)}

STATUS: BACKUP COMPLETED SUCCESSFULLY
READY FOR NUCLEAR REMEDIATION PROTOCOL
`;

    fs.writeFileSync(summaryFile, summary);

    console.log('=' .repeat(70));
    console.log('🎉 EMERGENCY BACKUP COMPLETED SUCCESSFULLY');
    console.log(`📁 Backup file: ${backupFile}`);
    console.log(`📋 Summary file: ${summaryFile}`);
    console.log(`📊 Total records backed up: ${Object.values(backupData).reduce((sum, arr) => sum + arr.length, 0)}`);
    console.log('🚀 READY FOR NUCLEAR REMEDIATION PROTOCOL');
    console.log('=' .repeat(70));

  } catch (error) {
    console.error('❌ EMERGENCY BACKUP FAILED:', error);
    throw error;
  }
}

// Run backup if called directly
if (require.main === module) {
  createEmergencyBackup()
    .then(() => {
      console.log('✅ Emergency backup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Emergency backup failed:', error);
      process.exit(1);
    });
}

export { createEmergencyBackup };
