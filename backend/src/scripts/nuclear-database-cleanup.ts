/**
 * NUCLEAR DATABASE CLEANUP SCRIPT
 * Eliminates ALL Prisma and Sequelize contamination from the system
 * ZERO TOLERANCE FOR INCOMPLETE ELIMINATION
 */

import fs from 'fs';
import path from 'path';
import { query, initializeDatabase } from '../config/database';

interface CleanupReport {
  prismaFilesRemoved: string[];
  sequelizeFilesRemoved: string[];
  tablesDropped: string[];
  dependenciesRemoved: string[];
  configsCleaned: string[];
  errors: string[];
}

async function executeNuclearCleanup(): Promise<CleanupReport> {
  console.log('☢️  NUCLEAR DATABASE CLEANUP INITIATED');
  console.log('🚨 ZERO TOLERANCE FOR INCOMPLETE ELIMINATION');
  console.log('=' .repeat(70));

  const report: CleanupReport = {
    prismaFilesRemoved: [],
    sequelizeFilesRemoved: [],
    tablesDropped: [],
    dependenciesRemoved: [],
    configsCleaned: [],
    errors: []
  };

  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('✅ Database connection established for cleanup');

    // STEP 1: DROP PRISMA TABLES
    console.log('☢️  STEP 1: ELIMINATING PRISMA TABLES...');
    const prismaTables = [
      '_prisma_migrations',
      'Admin', 'Message', 'Newsletter', 'PasswordHistory', 
      'Scholarship', 'SecurityEvent', 'SecuritySettings', 'User'
    ];

    for (const table of prismaTables) {
      try {
        await query(`DROP TABLE IF EXISTS "${table}" CASCADE`);
        report.tablesDropped.push(table);
        console.log(`✅ Eliminated Prisma table: ${table}`);
      } catch (error) {
        const errorMsg = `Failed to drop table ${table}: ${error}`;
        report.errors.push(errorMsg);
        console.log(`⚠️ ${errorMsg}`);
      }
    }

    // STEP 2: REMOVE PRISMA FILES
    console.log('☢️  STEP 2: ELIMINATING PRISMA FILES...');
    const backendDir = path.join(__dirname, '../..');
    
    // Remove prisma directory completely
    const prismaDir = path.join(backendDir, 'prisma');
    if (fs.existsSync(prismaDir)) {
      fs.rmSync(prismaDir, { recursive: true, force: true });
      report.prismaFilesRemoved.push(prismaDir);
      console.log('✅ Eliminated entire prisma directory');
    }

    // Remove Prisma-contaminated files
    const prismaContaminatedFiles = [
      'src/scripts/cleanupDuplicateData.ts',
      'src/scripts/createNewAdmin.ts',
      'src/scripts/fixAdminPrivileges.ts',
      'src/scripts/fixNonMainAdmin.ts',
      'src/scripts/seedScholarships.ts',
      'src/utils/cleanupUtils.ts'
    ];

    for (const file of prismaContaminatedFiles) {
      const fullPath = path.join(backendDir, file);
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        report.prismaFilesRemoved.push(fullPath);
        console.log(`✅ Eliminated Prisma-contaminated file: ${file}`);
      }
    }

    // STEP 3: REMOVE SEQUELIZE FILES
    console.log('☢️  STEP 3: ELIMINATING SEQUELIZE FILES...');
    const sequelizeContaminatedFiles = [
      'src/scripts/checkDatabase.ts',
      'src/scripts/cleanupDuplicateAdmins.ts',
      'src/scripts/createSequelizeAdmin.ts',
      'src/scripts/removeSequelize.ts',
      'src/scripts/completeMigration.ts',
      'src/scripts/updateControllers.ts'
    ];

    for (const file of sequelizeContaminatedFiles) {
      const fullPath = path.join(backendDir, file);
      if (fs.existsSync(fullPath)) {
        fs.unlinkSync(fullPath);
        report.sequelizeFilesRemoved.push(fullPath);
        console.log(`✅ Eliminated Sequelize-contaminated file: ${file}`);
      }
    }

    // STEP 4: CLEAN PACKAGE.JSON
    console.log('☢️  STEP 4: CLEANING PACKAGE DEPENDENCIES...');
    const packageJsonPath = path.join(backendDir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      // Remove Prisma dependencies
      const prismaPackages = ['prisma', '@prisma/client'];
      const sequelizePackages = ['sequelize', 'sequelize-cli', 'sqlite3'];
      
      let dependenciesChanged = false;
      
      [...prismaPackages, ...sequelizePackages].forEach(pkg => {
        if (packageJson.dependencies?.[pkg]) {
          delete packageJson.dependencies[pkg];
          report.dependenciesRemoved.push(pkg);
          dependenciesChanged = true;
          console.log(`✅ Removed dependency: ${pkg}`);
        }
        if (packageJson.devDependencies?.[pkg]) {
          delete packageJson.devDependencies[pkg];
          report.dependenciesRemoved.push(pkg);
          dependenciesChanged = true;
          console.log(`✅ Removed dev dependency: ${pkg}`);
        }
      });

      if (dependenciesChanged) {
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
        report.configsCleaned.push('package.json');
        console.log('✅ Package.json cleaned');
      }
    }

    // STEP 5: CLEAN TSCONFIG.JSON
    console.log('☢️  STEP 5: CLEANING TYPESCRIPT CONFIG...');
    const tsconfigPath = path.join(backendDir, 'tsconfig.json');
    if (fs.existsSync(tsconfigPath)) {
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
      
      // Remove Prisma/Sequelize exclusions
      if (tsconfig.exclude) {
        const originalLength = tsconfig.exclude.length;
        tsconfig.exclude = tsconfig.exclude.filter((item: string) => 
          !item.includes('prisma') && 
          !item.includes('sequelize') &&
          !item.includes('cleanupUtils')
        );
        
        if (tsconfig.exclude.length !== originalLength) {
          fs.writeFileSync(tsconfigPath, JSON.stringify(tsconfig, null, 2));
          report.configsCleaned.push('tsconfig.json');
          console.log('✅ TSConfig.json cleaned');
        }
      }
    }

    // STEP 6: VERIFY CLEANUP
    console.log('☢️  STEP 6: VERIFYING NUCLEAR CLEANUP...');
    const remainingTables = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    console.log('📊 Remaining database tables:');
    remainingTables.rows.forEach((row: any) => {
      console.log(`   - ${row.table_name}`);
    });

    // Generate cleanup report
    const reportPath = path.join(backendDir, 'backups', `nuclear_cleanup_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log('=' .repeat(70));
    console.log('☢️  NUCLEAR CLEANUP COMPLETED');
    console.log(`📁 Report: ${reportPath}`);
    console.log(`🗑️  Prisma files eliminated: ${report.prismaFilesRemoved.length}`);
    console.log(`🗑️  Sequelize files eliminated: ${report.sequelizeFilesRemoved.length}`);
    console.log(`🗑️  Tables dropped: ${report.tablesDropped.length}`);
    console.log(`🗑️  Dependencies removed: ${report.dependenciesRemoved.length}`);
    console.log(`⚠️  Errors encountered: ${report.errors.length}`);
    console.log('🚀 SYSTEM PURIFIED - READY FOR RECONSTRUCTION');
    console.log('=' .repeat(70));

  } catch (error) {
    const errorMsg = `Nuclear cleanup failed: ${error}`;
    report.errors.push(errorMsg);
    console.error('❌', errorMsg);
    throw error;
  }

  return report;
}

// Run cleanup if called directly
if (require.main === module) {
  executeNuclearCleanup()
    .then(() => {
      console.log('✅ Nuclear database cleanup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Nuclear database cleanup failed:', error);
      process.exit(1);
    });
}

export { executeNuclearCleanup };
