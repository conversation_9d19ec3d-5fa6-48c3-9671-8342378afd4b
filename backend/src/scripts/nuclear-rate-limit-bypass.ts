/**
 * NUCLEAR RATE LIMIT BYPASS SCRIPT
 * Emergency script to bypass rate limiting and verify admin login
 * ZERO TOLERANCE FOR LOGIN FAILURES
 */

import { query, initializeDatabase } from '../config/database';
const bcrypt = require('bcrypt');

interface AdminUser {
  id: number;
  email: string;
  password_hash: string;
  failed_login_attempts: number;
  lock_until?: Date;
  is_main_admin: boolean;
}

async function nuclearRateLimitBypass(): Promise<void> {
  console.log('☢️  NUCLEAR RATE LIMIT BYPASS INITIATED');
  console.log('🚨 EMERGENCY ADMIN LOGIN RESTORATION');
  console.log('=' .repeat(70));

  try {
    // Initialize database connection
    await initializeDatabase();
    console.log('✅ Database connection established');

    // Step 1: Check current admin status
    console.log('🔍 STEP 1: Checking admin account status...');
    const adminResult = await query('SELECT * FROM admins WHERE email = $1', ['<EMAIL>']);
    
    if (adminResult.rows.length === 0) {
      console.log('❌ No admin found <NAME_EMAIL>');
      
      // Create emergency admin
      console.log('🚨 CREATING EMERGENCY ADMIN ACCOUNT...');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      await query(`
        INSERT INTO admins (email, password_hash, is_main_admin, failed_login_attempts, created_at, updated_at)
        VALUES ($1, $2, $3, $4, NOW(), NOW())
      `, ['<EMAIL>', hashedPassword, true, 0]);
      
      console.log('✅ Emergency admin account created successfully');
    } else {
      const admin: AdminUser = adminResult.rows[0];
      console.log('📊 Current admin status:');
      console.log(`   - ID: ${admin.id}`);
      console.log(`   - Email: ${admin.email}`);
      console.log(`   - Failed attempts: ${admin.failed_login_attempts}`);
      console.log(`   - Locked until: ${admin.lock_until || 'Not locked'}`);
      console.log(`   - Is main admin: ${admin.is_main_admin}`);
    }

    // Step 2: Reset admin account completely
    console.log('🔧 STEP 2: Resetting admin account...');
    await query(`
      UPDATE admins 
      SET failed_login_attempts = 0, 
          lock_until = NULL,
          updated_at = NOW()
      WHERE email = $1
    `, ['<EMAIL>']);
    console.log('✅ Admin account reset successfully');

    // Step 3: Verify password hash
    console.log('🔐 STEP 3: Verifying password hash...');
    const verifyResult = await query('SELECT password_hash FROM admins WHERE email = $1', ['<EMAIL>']);
    
    if (verifyResult.rows.length > 0) {
      const storedHash = verifyResult.rows[0].password_hash;
      const isValidPassword = await bcrypt.compare('admin123', storedHash);
      
      if (isValidPassword) {
        console.log('✅ Password verification successful');
      } else {
        console.log('❌ Password verification failed - updating password...');
        const newHash = await bcrypt.hash('admin123', 12);
        await query('UPDATE admins SET password_hash = $1 WHERE email = $2', [newHash, '<EMAIL>']);
        console.log('✅ Password updated successfully');
      }
    }

    // Step 4: Test direct login logic (bypass rate limiting)
    console.log('🧪 STEP 4: Testing login logic...');
    const loginTestResult = await query(`
      SELECT id, email, password_hash, is_main_admin, failed_login_attempts, lock_until
      FROM admins 
      WHERE email = $1
    `, ['<EMAIL>']);

    if (loginTestResult.rows.length > 0) {
      const admin = loginTestResult.rows[0];
      
      // Check if account is locked
      if (admin.lock_until && new Date(admin.lock_until) > new Date()) {
        console.log('❌ Account is still locked');
        // Force unlock
        await query('UPDATE admins SET lock_until = NULL WHERE email = $1', ['<EMAIL>']);
        console.log('✅ Account forcefully unlocked');
      }

      // Verify password
      const passwordValid = await bcrypt.compare('admin123', admin.password_hash);
      if (passwordValid) {
        console.log('✅ Login logic test successful');
        console.log('📊 Admin ready for login:');
        console.log(`   - ID: ${admin.id}`);
        console.log(`   - Email: ${admin.email}`);
        console.log(`   - Is main admin: ${admin.is_main_admin}`);
        console.log(`   - Failed attempts: ${admin.failed_login_attempts}`);
        console.log(`   - Lock status: UNLOCKED`);
      } else {
        console.log('❌ Password still invalid after update');
      }
    }

    // Step 5: Create bypass login endpoint test
    console.log('🚀 STEP 5: Creating bypass login test...');
    
    // Simulate successful login without rate limiting
    const jwt = require('jsonwebtoken');
    const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
    
    const finalAdmin = await query('SELECT * FROM admins WHERE email = $1', ['<EMAIL>']);
    if (finalAdmin.rows.length > 0) {
      const admin = finalAdmin.rows[0];
      
      // Generate JWT token
      const token = jwt.sign(
        {
          id: admin.id,
          email: admin.email,
          role: 'admin',
          isMainAdmin: admin.is_main_admin
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      console.log('✅ JWT token generated successfully');
      console.log('🔑 Token preview:', token.substring(0, 50) + '...');
      
      // Test token verification
      try {
        const decoded = jwt.verify(token, JWT_SECRET);
        console.log('✅ Token verification successful');
        console.log('📊 Decoded payload:', {
          id: (decoded as any).id,
          email: (decoded as any).email,
          role: (decoded as any).role
        });
      } catch (error) {
        console.log('❌ Token verification failed:', error);
      }
    }

    console.log('=' .repeat(70));
    console.log('☢️  NUCLEAR RATE LIMIT BYPASS COMPLETE');
    console.log('✅ Admin login should now work');
    console.log('🔑 Credentials: <EMAIL> / admin123');
    console.log('🚀 READY FOR ADMIN PORTAL ACCESS');
    console.log('=' .repeat(70));

  } catch (error) {
    console.error('❌ Nuclear bypass failed:', error);
    throw error;
  }
}

// Run bypass if called directly
if (require.main === module) {
  nuclearRateLimitBypass()
    .then(() => {
      console.log('✅ Nuclear rate limit bypass completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Nuclear rate limit bypass failed:', error);
      process.exit(1);
    });
}

export { nuclearRateLimitBypass };
