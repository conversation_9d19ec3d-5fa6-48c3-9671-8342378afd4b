/**
 * Direct test of Guide model to isolate the issue
 */

const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import the Guide model
const { Guide } = require('./dist/models/Guide');

async function testGuides() {
  try {
    console.log('🧪 Testing Guide.findAll() directly...');
    
    const guides = await Guide.findAll({
      limit: 5,
      offset: 0,
      orderBy: 'created_at',
      orderDirection: 'DESC'
    });
    
    console.log('✅ Success! Found guides:', guides.length);
    console.log('Sample guide:', guides[0] ? {
      id: guides[0].id,
      title: guides[0].title,
      category: guides[0].category
    } : 'No guides found');
    
  } catch (error) {
    console.error('❌ Error testing guides:', error);
  }
}

testGuides();
