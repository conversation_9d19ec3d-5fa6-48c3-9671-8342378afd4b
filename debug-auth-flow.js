/**
 * Debug Authentication Flow
 * 
 * This script debugs the authentication flow to identify why
 * cookies are not persisting between requests.
 */

const axios = require('axios');

async function debugAuthFlow() {
  console.log('🔍 Debugging Authentication Flow...\n');
  
  // Create axios instance with detailed cookie handling
  const api = axios.create({
    baseURL: 'http://localhost:5000',
    timeout: 10000,
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'MaBourse-Debug-Client/1.0'
    }
  });
  
  let cookies = '';
  
  try {
    // Step 1: Login and capture cookies
    console.log('1️⃣ Testing login and cookie capture...');
    const loginResponse = await api.post('/api/auth/admin/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    console.log(`✅ Login Status: ${loginResponse.status}`);
    console.log(`📝 Login Message: ${loginResponse.data.message}`);
    
    // Check response headers for cookies
    const setCookieHeaders = loginResponse.headers['set-cookie'];
    if (setCookieHeaders) {
      console.log('\n🍪 Cookies received from server:');
      setCookieHeaders.forEach((cookie, index) => {
        console.log(`   ${index + 1}. ${cookie}`);
        
        // Extract cookie name and value
        const [nameValue] = cookie.split(';');
        const [name, value] = nameValue.split('=');
        console.log(`      Name: ${name}, Value: ${value ? value.substring(0, 20) + '...' : 'empty'}`);
      });
      
      // Store cookies for next request
      cookies = setCookieHeaders.join('; ');
    } else {
      console.log('❌ No cookies received from login response!');
      return false;
    }
    
    // Step 2: Test authenticated request with cookies
    console.log('\n2️⃣ Testing authenticated request with cookies...');
    
    try {
      const statsResponse = await api.get('/api/admin/stats', {
        headers: {
          'Cookie': cookies
        }
      });
      
      console.log(`✅ Authenticated request successful: ${statsResponse.status}`);
      console.log(`📊 Stats data: ${JSON.stringify(statsResponse.data)}`);
      return true;
      
    } catch (authError) {
      console.log(`❌ Authenticated request failed: ${authError.response?.status} - ${authError.response?.data?.message}`);
      
      // Debug the request
      console.log('\n🔍 Debug information:');
      console.log(`   Request URL: ${authError.config?.url}`);
      console.log(`   Request Headers: ${JSON.stringify(authError.config?.headers, null, 2)}`);
      console.log(`   Cookies sent: ${cookies.substring(0, 100)}...`);
      
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Login failed: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data: ${JSON.stringify(error.response.data)}`);
    }
    return false;
  }
}

/**
 * Test cookie parsing and JWT verification
 */
async function testCookieHandling() {
  console.log('\n🍪 Testing Cookie Handling...\n');
  
  try {
    // Test the debug endpoint
    const debugResponse = await axios.get('http://localhost:5000/api/auth/debug', {
      withCredentials: true
    });
    
    console.log('🔍 Debug endpoint response:');
    console.log(JSON.stringify(debugResponse.data, null, 2));
    
  } catch (error) {
    console.log(`❌ Debug endpoint failed: ${error.message}`);
  }
}

/**
 * Test with manual cookie creation
 */
async function testManualCookies() {
  console.log('\n🔧 Testing Manual Cookie Creation...\n');
  
  try {
    // First login to get a token
    const loginResponse = await axios.post('http://localhost:5000/api/auth/admin/login', {
      email: '<EMAIL>',
      password: 'admin123'
    }, {
      withCredentials: true
    });
    
    console.log('✅ Login successful for manual test');
    
    // Extract the auth token from set-cookie header
    const setCookieHeaders = loginResponse.headers['set-cookie'];
    let authToken = '';
    
    if (setCookieHeaders) {
      for (const cookie of setCookieHeaders) {
        if (cookie.startsWith('auth_token=')) {
          authToken = cookie.split(';')[0].split('=')[1];
          break;
        }
      }
    }
    
    if (authToken) {
      console.log(`🔑 Extracted auth token: ${authToken.substring(0, 20)}...`);
      
      // Test with manual cookie
      const statsResponse = await axios.get('http://localhost:5000/api/admin/stats', {
        headers: {
          'Cookie': `auth_token=${authToken}`
        }
      });
      
      console.log(`✅ Manual cookie test successful: ${statsResponse.status}`);
      console.log(`📊 Data: ${JSON.stringify(statsResponse.data)}`);
      return true;
      
    } else {
      console.log('❌ Could not extract auth token from cookies');
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Manual cookie test failed: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Message: ${error.response.data?.message}`);
    }
    return false;
  }
}

/**
 * Main debug function
 */
async function runAuthFlowDebug() {
  console.log('🚀 Starting Authentication Flow Debug...\n');
  console.log('=' .repeat(60));
  
  // Test 1: Basic auth flow
  const basicFlowWorking = await debugAuthFlow();
  
  console.log('\n' + '=' .repeat(60));
  
  // Test 2: Cookie handling
  await testCookieHandling();
  
  console.log('\n' + '=' .repeat(60));
  
  // Test 3: Manual cookies
  const manualCookiesWorking = await testManualCookies();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 DEBUG RESULTS');
  console.log('=' .repeat(60));
  
  console.log(`🔐 Basic Auth Flow: ${basicFlowWorking ? '✅ WORKING' : '❌ BROKEN'}`);
  console.log(`🍪 Manual Cookies: ${manualCookiesWorking ? '✅ WORKING' : '❌ BROKEN'}`);
  
  if (manualCookiesWorking && !basicFlowWorking) {
    console.log('\n🔍 DIAGNOSIS: Cookie persistence issue in axios withCredentials');
    console.log('💡 SOLUTION: The authentication system works, but axios cookie handling needs fixing');
  } else if (!manualCookiesWorking) {
    console.log('\n🔍 DIAGNOSIS: JWT token or authentication middleware issue');
    console.log('💡 SOLUTION: Check JWT secret, token expiration, or middleware configuration');
  } else {
    console.log('\n🎉 DIAGNOSIS: Authentication flow is working correctly!');
  }
  
  return basicFlowWorking || manualCookiesWorking;
}

// Run debug
if (require.main === module) {
  runAuthFlowDebug()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Debug failed:', error);
      process.exit(1);
    });
}

module.exports = { runAuthFlowDebug };
