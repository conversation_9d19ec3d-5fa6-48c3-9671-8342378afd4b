/**
 * Admin Portal Comprehensive Diagnosis
 * 
 * This script diagnoses all admin portal issues including authentication,
 * data fetching, and security vulnerabilities.
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:5000';

/**
 * Test admin endpoints without authentication
 */
async function testUnauthenticatedAccess() {
  console.log('🔒 Testing Unauthenticated Access to Admin Endpoints...\n');
  
  const adminEndpoints = [
    { name: 'Admin Stats', url: '/api/admin/stats' },
    { name: 'Current Admin', url: '/api/admin/me' },
    { name: 'All Admins', url: '/api/admin/all' },
    { name: 'Admin Current', url: '/api/admin/current' },
    { name: 'Newsletter Subscribers', url: '/api/newsletter/subscribers' },
    { name: 'Messages', url: '/api/messages' },
    { name: 'Analytics', url: '/api/admin/analytics' }
  ];
  
  const results = {
    protected: [],
    unprotected: [],
    errors: []
  };
  
  for (const endpoint of adminEndpoints) {
    try {
      console.log(`🧪 Testing ${endpoint.name}...`);
      
      const response = await axios.get(`${BACKEND_URL}${endpoint.url}`, {
        timeout: 5000,
        // Explicitly don't send credentials
        withCredentials: false
      });
      
      if (response.status === 200) {
        console.log(`❌ ${endpoint.name} - UNPROTECTED (Status: ${response.status})`);
        results.unprotected.push(endpoint.name);
        
        // Log what data is returned
        if (response.data) {
          console.log(`   📊 Data returned: ${JSON.stringify(response.data).substring(0, 100)}...`);
        }
      }
      
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`✅ ${endpoint.name} - PROPERLY PROTECTED (401 Unauthorized)`);
        results.protected.push(endpoint.name);
      } else if (error.response?.status === 403) {
        console.log(`✅ ${endpoint.name} - PROPERLY PROTECTED (403 Forbidden)`);
        results.protected.push(endpoint.name);
      } else {
        console.log(`⚠️  ${endpoint.name} - ERROR: ${error.message}`);
        results.errors.push({ name: endpoint.name, error: error.message });
      }
    }
    
    console.log(''); // Empty line
  }
  
  return results;
}

/**
 * Test authentication flow
 */
async function testAuthenticationFlow() {
  console.log('🔐 Testing Authentication Flow...\n');
  
  const api = axios.create({
    baseURL: BACKEND_URL,
    timeout: 10000,
    withCredentials: true
  });
  
  try {
    // Step 1: Test login
    console.log('1️⃣ Testing admin login...');
    const loginResponse = await api.post('/api/auth/admin/login', {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    if (loginResponse.status === 200) {
      console.log('✅ Login successful');
      console.log(`   📊 Response: ${loginResponse.data.message}`);
      
      // Step 2: Test authenticated endpoint
      console.log('\n2️⃣ Testing authenticated endpoint access...');
      const statsResponse = await api.get('/api/admin/stats');
      
      if (statsResponse.status === 200) {
        console.log('✅ Authenticated access working');
        console.log(`   📊 Stats: ${JSON.stringify(statsResponse.data)}`);
      }
      
      // Step 3: Test logout
      console.log('\n3️⃣ Testing logout...');
      const logoutResponse = await api.post('/api/auth/admin/logout');
      
      if (logoutResponse.status === 200) {
        console.log('✅ Logout successful');
        
        // Step 4: Test access after logout
        console.log('\n4️⃣ Testing access after logout...');
        try {
          await api.get('/api/admin/stats');
          console.log('❌ Still have access after logout - SECURITY ISSUE!');
          return false;
        } catch (error) {
          if (error.response?.status === 401) {
            console.log('✅ Access properly denied after logout');
            return true;
          } else {
            console.log(`⚠️  Unexpected error after logout: ${error.message}`);
            return false;
          }
        }
      }
    }
    
  } catch (error) {
    if (error.response?.status === 429) {
      console.log('⚠️  Rate limited - authentication system is working');
      return true;
    } else {
      console.log(`❌ Authentication flow failed: ${error.message}`);
      return false;
    }
  }
}

/**
 * Test frontend authentication check
 */
async function testFrontendAuthCheck() {
  console.log('🌐 Testing Frontend Authentication Check...\n');
  
  try {
    // Test the endpoint that frontend uses to check auth status
    const response = await axios.get(`${BACKEND_URL}/api/auth/admin/me`, {
      timeout: 5000,
      withCredentials: true
    });
    
    if (response.status === 200) {
      console.log('❌ Frontend auth check endpoint is unprotected!');
      console.log(`   📊 Data: ${JSON.stringify(response.data)}`);
      return false;
    }
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Frontend auth check properly protected');
      return true;
    } else {
      console.log(`⚠️  Unexpected error: ${error.message}`);
      return false;
    }
  }
}

/**
 * Test CORS and security headers
 */
async function testSecurityHeaders() {
  console.log('🛡️  Testing Security Headers...\n');
  
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`, {
      timeout: 5000
    });
    
    const headers = response.headers;
    const securityHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'strict-transport-security',
      'content-security-policy'
    ];
    
    let securityScore = 0;
    
    for (const header of securityHeaders) {
      if (headers[header]) {
        console.log(`✅ ${header}: ${headers[header]}`);
        securityScore++;
      } else {
        console.log(`❌ ${header}: Missing`);
      }
    }
    
    console.log(`\n📊 Security Score: ${securityScore}/${securityHeaders.length}`);
    
    return securityScore >= 3; // At least 3 security headers should be present
    
  } catch (error) {
    console.log(`❌ Security headers test failed: ${error.message}`);
    return false;
  }
}

/**
 * Main diagnosis function
 */
async function runComprehensiveDiagnosis() {
  console.log('🚀 Starting Comprehensive Admin Portal Diagnosis...\n');
  console.log('=' .repeat(70));
  
  // Test 1: Unauthenticated access
  const unauthResults = await testUnauthenticatedAccess();
  
  console.log('=' .repeat(70));
  
  // Test 2: Authentication flow
  const authFlowWorking = await testAuthenticationFlow();
  
  console.log('\n' + '=' .repeat(70));
  
  // Test 3: Frontend auth check
  const frontendAuthSecure = await testFrontendAuthCheck();
  
  console.log('\n' + '=' .repeat(70));
  
  // Test 4: Security headers
  const securityHeadersPresent = await testSecurityHeaders();
  
  console.log('\n' + '=' .repeat(70));
  console.log('📊 COMPREHENSIVE DIAGNOSIS RESULTS');
  console.log('=' .repeat(70));
  
  console.log('\n🔒 AUTHENTICATION SECURITY:');
  console.log(`✅ Protected Endpoints: ${unauthResults.protected.length}`);
  console.log(`❌ Unprotected Endpoints: ${unauthResults.unprotected.length}`);
  console.log(`⚠️  Error Endpoints: ${unauthResults.errors.length}`);
  
  if (unauthResults.unprotected.length > 0) {
    console.log('\n🚨 CRITICAL SECURITY VULNERABILITIES:');
    unauthResults.unprotected.forEach(endpoint => {
      console.log(`   - ${endpoint} is accessible without authentication`);
    });
  }
  
  console.log(`\n🔐 Authentication Flow: ${authFlowWorking ? '✅ WORKING' : '❌ BROKEN'}`);
  console.log(`🌐 Frontend Auth Check: ${frontendAuthSecure ? '✅ SECURE' : '❌ VULNERABLE'}`);
  console.log(`🛡️  Security Headers: ${securityHeadersPresent ? '✅ PRESENT' : '❌ MISSING'}`);
  
  const overallSecure = unauthResults.unprotected.length === 0 && 
                       authFlowWorking && 
                       frontendAuthSecure && 
                       securityHeadersPresent;
  
  if (overallSecure) {
    console.log('\n🎉 ADMIN PORTAL SECURITY: EXCELLENT');
    console.log('✅ All security checks passed!');
  } else {
    console.log('\n🚨 ADMIN PORTAL SECURITY: CRITICAL ISSUES DETECTED');
    console.log('❌ Immediate action required to fix security vulnerabilities!');
    
    console.log('\n🔧 REQUIRED FIXES:');
    if (unauthResults.unprotected.length > 0) {
      console.log('1. Add authentication middleware to unprotected endpoints');
    }
    if (!authFlowWorking) {
      console.log('2. Fix authentication flow and session management');
    }
    if (!frontendAuthSecure) {
      console.log('3. Secure frontend authentication check endpoint');
    }
    if (!securityHeadersPresent) {
      console.log('4. Add missing security headers');
    }
  }
  
  return overallSecure;
}

// Run comprehensive diagnosis
if (require.main === module) {
  runComprehensiveDiagnosis()
    .then(secure => {
      process.exit(secure ? 0 : 1);
    })
    .catch(error => {
      console.error('Diagnosis failed:', error);
      process.exit(1);
    });
}

module.exports = { runComprehensiveDiagnosis };
