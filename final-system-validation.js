/**
 * Final System Validation Script
 * 
 * Comprehensive test suite to validate the entire MaBourse application
 * including backend APIs, database connectivity, image serving, and system health.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BACKEND_URL = 'http://localhost:5000';
const FRONTEND_URL = 'http://localhost:3000';

/**
 * Test database connectivity and data integrity
 */
async function testDatabaseConnectivity() {
  console.log('🗄️  Testing Database Connectivity...');
  
  try {
    const response = await axios.get(`${BACKEND_URL}/api/test-db`);
    
    if (response.data.success) {
      console.log('✅ Database connection - PASSED');
      console.log(`   📊 Admins: ${response.data.data.adminCount}`);
      console.log(`   👥 Users: ${response.data.data.userCount}`);
      console.log(`   🎓 Scholarships: ${response.data.data.scholarshipCount}`);
      return true;
    } else {
      console.log('❌ Database connection - FAILED');
      return false;
    }
  } catch (error) {
    console.log(`❌ Database connection - ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Test all API endpoints
 */
async function testAPIEndpoints() {
  console.log('\n📡 Testing API Endpoints...');
  
  const endpoints = [
    { name: 'Health Check', url: '/api/health', method: 'GET' },
    { name: 'Scholarships List', url: '/api/scholarships?limit=5', method: 'GET' },
    { name: 'Scholarship Search', url: '/api/scholarships/search?q=DAAD&limit=3', method: 'GET' },
    { name: 'Opportunities List', url: '/api/opportunities?limit=5', method: 'GET' },
    { name: 'Countries List', url: '/api/countries', method: 'GET' },
    { name: 'Guides List', url: '/api/guides?limit=3', method: 'GET' },
    { name: 'Newsletter Endpoint', url: '/api/newsletter', method: 'GET' }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios({
        method: endpoint.method,
        url: `${BACKEND_URL}${endpoint.url}`,
        timeout: 10000
      });
      
      if (response.status === 200) {
        console.log(`✅ ${endpoint.name} - PASSED`);
        passed++;
      } else {
        console.log(`❌ ${endpoint.name} - FAILED (Status: ${response.status})`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name} - ERROR: ${error.message}`);
      failed++;
    }
  }
  
  console.log(`\n📊 API Endpoints: ${passed}/${passed + failed} passed`);
  return failed === 0;
}

/**
 * Test image serving and thumbnails
 */
async function testImageServing() {
  console.log('\n🖼️  Testing Image Serving...');
  
  try {
    // Get a scholarship with an image
    const scholarshipsResponse = await axios.get(`${BACKEND_URL}/api/scholarships?limit=1`);
    
    if (!scholarshipsResponse.data.data || scholarshipsResponse.data.data.length === 0) {
      console.log('⚠️  No scholarships found for image testing');
      return false;
    }
    
    const scholarship = scholarshipsResponse.data.data[0];
    
    if (!scholarship.thumbnail) {
      console.log('⚠️  No thumbnail found in scholarship data');
      return false;
    }
    
    // Test original image
    const imageUrl = `${BACKEND_URL}${scholarship.thumbnail}`;
    const imageResponse = await axios.head(imageUrl);
    
    if (imageResponse.status !== 200) {
      console.log('❌ Original image serving - FAILED');
      return false;
    }
    
    console.log('✅ Original image serving - PASSED');
    console.log(`   📸 URL: ${imageUrl}`);
    console.log(`   📏 Type: ${imageResponse.headers['content-type']}`);
    console.log(`   📦 Size: ${imageResponse.headers['content-length']} bytes`);
    
    // Test thumbnail
    const filename = path.basename(scholarship.thumbnail, path.extname(scholarship.thumbnail));
    const thumbnailUrl = `${BACKEND_URL}/uploads/thumbnails/scholarships/${filename}_card.webp`;
    
    try {
      const thumbnailResponse = await axios.head(thumbnailUrl);
      if (thumbnailResponse.status === 200) {
        console.log('✅ Thumbnail serving - PASSED');
        console.log(`   🖼️  Thumbnail URL: ${thumbnailUrl}`);
        console.log(`   📏 Type: ${thumbnailResponse.headers['content-type']}`);
      }
    } catch (error) {
      console.log(`⚠️  Thumbnail serving - WARNING: ${error.message}`);
    }
    
    return true;
    
  } catch (error) {
    console.log(`❌ Image serving test - ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Test file system structure
 */
async function testFileSystemStructure() {
  console.log('\n📁 Testing File System Structure...');
  
  const requiredDirectories = [
    'backend/uploads',
    'backend/uploads/scholarships',
    'backend/uploads/opportunities',
    'backend/uploads/thumbnails',
    'backend/uploads/thumbnails/scholarships',
    'backend/uploads/thumbnails/opportunities'
  ];
  
  let allExist = true;
  
  for (const dir of requiredDirectories) {
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir).length;
      console.log(`✅ ${dir} - EXISTS (${files} files)`);
    } else {
      console.log(`❌ ${dir} - MISSING`);
      allExist = false;
    }
  }
  
  return allExist;
}

/**
 * Test security headers and CORS
 */
async function testSecurityAndCORS() {
  console.log('\n🔒 Testing Security Headers and CORS...');
  
  try {
    const response = await axios.get(`${BACKEND_URL}/api/health`, {
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });
    
    const headers = response.headers;
    
    // Check CORS headers
    if (headers['access-control-allow-credentials'] === 'true') {
      console.log('✅ CORS credentials - PASSED');
    } else {
      console.log('❌ CORS credentials - FAILED');
    }
    
    // Check security headers
    const securityHeaders = [
      'content-security-policy',
      'x-content-type-options',
      'x-frame-options',
      'strict-transport-security'
    ];
    
    let securityPassed = 0;
    for (const header of securityHeaders) {
      if (headers[header]) {
        console.log(`✅ ${header} - PRESENT`);
        securityPassed++;
      } else {
        console.log(`⚠️  ${header} - MISSING`);
      }
    }
    
    console.log(`📊 Security headers: ${securityPassed}/${securityHeaders.length} present`);
    
    return true;
    
  } catch (error) {
    console.log(`❌ Security/CORS test - ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Test server performance
 */
async function testServerPerformance() {
  console.log('\n⚡ Testing Server Performance...');
  
  const testRequests = [
    { name: 'Health Check', url: '/api/health' },
    { name: 'Scholarships', url: '/api/scholarships?limit=10' },
    { name: 'Search', url: '/api/scholarships/search?q=scholarship&limit=5' }
  ];
  
  for (const test of testRequests) {
    try {
      const startTime = Date.now();
      const response = await axios.get(`${BACKEND_URL}${test.url}`);
      const duration = Date.now() - startTime;
      
      if (response.status === 200) {
        const status = duration < 1000 ? '✅ FAST' : duration < 3000 ? '⚠️  SLOW' : '❌ VERY SLOW';
        console.log(`${status} ${test.name} - ${duration}ms`);
      }
    } catch (error) {
      console.log(`❌ ${test.name} - ERROR: ${error.message}`);
    }
  }
  
  return true;
}

/**
 * Generate system report
 */
function generateSystemReport(results) {
  const report = `
# MaBourse System Validation Report
Generated: ${new Date().toISOString()}

## Test Results Summary
- Database Connectivity: ${results.database ? '✅ PASSED' : '❌ FAILED'}
- API Endpoints: ${results.api ? '✅ PASSED' : '❌ FAILED'}
- Image Serving: ${results.images ? '✅ PASSED' : '❌ FAILED'}
- File System: ${results.filesystem ? '✅ PASSED' : '❌ FAILED'}
- Security/CORS: ${results.security ? '✅ PASSED' : '❌ FAILED'}
- Performance: ${results.performance ? '✅ PASSED' : '❌ FAILED'}

## Overall Status
${Object.values(results).every(r => r) ? '🎉 ALL SYSTEMS OPERATIONAL' : '⚠️  SOME ISSUES DETECTED'}

## System Information
- Backend URL: ${BACKEND_URL}
- Frontend URL: ${FRONTEND_URL}
- Node.js Version: ${process.version}
- Platform: ${process.platform}

## Next Steps
${Object.values(results).every(r => r) 
  ? '✅ System is ready for production use!'
  : '⚠️  Please review failed tests and address issues before deployment.'
}
`;

  fs.writeFileSync('system-validation-report.md', report);
  console.log('\n📄 System report saved to: system-validation-report.md');
}

/**
 * Main validation function
 */
async function runSystemValidation() {
  console.log('🚀 Starting Final System Validation...\n');
  console.log('=' .repeat(60));
  
  const results = {
    database: await testDatabaseConnectivity(),
    api: await testAPIEndpoints(),
    images: await testImageServing(),
    filesystem: await testFileSystemStructure(),
    security: await testSecurityAndCORS(),
    performance: await testServerPerformance()
  };
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 FINAL VALIDATION RESULTS');
  console.log('=' .repeat(60));
  
  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;
  
  console.log(`✅ Passed: ${passed}/${total} tests`);
  console.log(`📈 Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
  
  if (passed === total) {
    console.log('\n🎉 SYSTEM VALIDATION SUCCESSFUL!');
    console.log('🚀 MaBourse application is ready for production use!');
  } else {
    console.log('\n⚠️  SYSTEM VALIDATION INCOMPLETE');
    console.log('🔧 Please address the failed tests before deployment.');
  }
  
  generateSystemReport(results);
  
  return passed === total;
}

// Run validation if this script is executed directly
if (require.main === module) {
  runSystemValidation()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Validation failed:', error);
      process.exit(1);
    });
}

module.exports = { runSystemValidation };
