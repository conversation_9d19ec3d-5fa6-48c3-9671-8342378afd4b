/**
 * NUCLEAR COMPONENT STANDARDIZATION SCRIPT
 * Converts ALL admin components to Ant Design - ZERO HEROICONS TOLERANCE
 */

import fs from 'fs';
import path from 'path';

interface ComponentStandardization {
  heroiconsFiles: string[];
  conversionsApplied: string[];
  errors: string[];
}

// Heroicons to Ant Design icon mapping
const ICON_MAPPING: Record<string, string> = {
  'PlusIcon': 'PlusOutlined',
  'PencilIcon': 'EditOutlined', 
  'TrashIcon': 'DeleteOutlined',
  'EyeIcon': 'EyeOutlined',
  'MagnifyingGlassIcon': 'SearchOutlined',
  'FunnelIcon': 'FilterOutlined',
  'DocumentArrowUpIcon': 'UploadOutlined',
  'EnvelopeIcon': 'MailOutlined',
  'EnvelopeOpenIcon': 'MailOutlined',
  'CheckIcon': 'CheckOutlined',
  'XMarkIcon': 'CloseOutlined',
  'UserIcon': 'UserOutlined',
  'CogIcon': 'SettingOutlined',
  'HomeIcon': 'HomeOutlined',
  'ChartBarIcon': 'BarChartOutlined',
  'DocumentIcon': 'FileOutlined',
  'BellIcon': 'BellOutlined',
  'ShieldCheckIcon': 'SafetyOutlined',
  'ExclamationTriangleIcon': 'ExclamationTriangleOutlined'
};

async function standardizeComponents(): Promise<ComponentStandardization> {
  console.log('☢️  NUCLEAR COMPONENT STANDARDIZATION INITIATED');
  console.log('🚨 ZERO TOLERANCE FOR HEROICONS');
  console.log('=' .repeat(70));

  const report: ComponentStandardization = {
    heroiconsFiles: [],
    conversionsApplied: [],
    errors: []
  };

  const srcDir = path.join(__dirname, 'src');

  try {
    // Find all files with Heroicons imports
    const heroiconsFiles = await findHeroiconsFiles(srcDir);
    report.heroiconsFiles = heroiconsFiles;
    
    console.log(`🔍 Found ${heroiconsFiles.length} files with Heroicons contamination`);

    // Convert each file
    for (const filePath of heroiconsFiles) {
      try {
        console.log(`☢️  Converting: ${path.relative(srcDir, filePath)}`);
        await convertFileToAntDesign(filePath);
        report.conversionsApplied.push(filePath);
        console.log(`✅ Converted: ${path.relative(srcDir, filePath)}`);
      } catch (error) {
        const errorMsg = `Failed to convert ${filePath}: ${error}`;
        report.errors.push(errorMsg);
        console.log(`❌ ${errorMsg}`);
      }
    }

    console.log('=' .repeat(70));
    console.log('☢️  COMPONENT STANDARDIZATION COMPLETE');
    console.log(`✅ Files converted: ${report.conversionsApplied.length}`);
    console.log(`❌ Errors: ${report.errors.length}`);
    console.log('🚀 ALL COMPONENTS NOW USE ANT DESIGN');
    console.log('=' .repeat(70));

  } catch (error) {
    console.error('❌ Component standardization failed:', error);
    throw error;
  }

  return report;
}

async function findHeroiconsFiles(dir: string): Promise<string[]> {
  const results: string[] = [];
  
  function scanDirectory(currentDir: string) {
    if (!fs.existsSync(currentDir)) return;
    
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and build directories
        if (!item.includes('node_modules') && !item.includes('build') && !item.includes('.git')) {
          scanDirectory(fullPath);
        }
      } else if (stat.isFile() && (item.endsWith('.tsx') || item.endsWith('.ts'))) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          if (content.includes('@heroicons/react') || content.includes('heroicons')) {
            results.push(fullPath);
          }
        } catch (error) {
          // Skip files that can't be read
        }
      }
    }
  }
  
  scanDirectory(dir);
  return results;
}

async function convertFileToAntDesign(filePath: string): Promise<void> {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Replace Heroicons imports with Ant Design imports
  const heroiconsImportRegex = /import\s*{\s*([^}]+)\s*}\s*from\s*['"]@heroicons\/react\/24\/outline['"];?/g;
  const matches = content.match(heroiconsImportRegex);
  
  if (matches) {
    for (const match of matches) {
      // Extract icon names from import
      const iconNamesMatch = match.match(/{\s*([^}]+)\s*}/);
      if (iconNamesMatch) {
        const iconNames = iconNamesMatch[1]
          .split(',')
          .map(name => name.trim())
          .filter(name => name.length > 0);
        
        // Convert to Ant Design icons
        const antdIcons = iconNames
          .map(iconName => ICON_MAPPING[iconName] || iconName)
          .filter(iconName => iconName);
        
        if (antdIcons.length > 0) {
          const antdImport = `import { ${antdIcons.join(', ')} } from '@ant-design/icons';`;
          content = content.replace(match, antdImport);
        } else {
          // Remove the import if no mappings found
          content = content.replace(match, '');
        }
      }
    }
  }
  
  // Replace icon usage in JSX
  Object.entries(ICON_MAPPING).forEach(([heroIcon, antdIcon]) => {
    // Replace JSX usage: <PlusIcon /> -> <PlusOutlined />
    const jsxRegex = new RegExp(`<${heroIcon}([^>]*?)\\s*/>`, 'g');
    content = content.replace(jsxRegex, `<${antdIcon}$1 />`);
    
    // Replace JSX usage with children: <PlusIcon>...</PlusIcon> -> <PlusOutlined>...</PlusOutlined>
    const jsxWithChildrenRegex = new RegExp(`<${heroIcon}([^>]*?)>(.*?)</${heroIcon}>`, 'g');
    content = content.replace(jsxWithChildrenRegex, `<${antdIcon}$1>$2</${antdIcon}>`);
  });
  
  // Add Ant Design imports if not present
  if (!content.includes("from 'antd'") && !content.includes('from "antd"')) {
    // Find existing imports to insert after
    const importLines = content.split('\n').filter(line => line.trim().startsWith('import'));
    if (importLines.length > 0) {
      const lastImportIndex = content.lastIndexOf(importLines[importLines.length - 1]);
      const insertPosition = content.indexOf('\n', lastImportIndex) + 1;
      
      // Add common Ant Design imports
      const antdImports = `import { Button, Table, Space, Modal, Form, Input, Select, Switch, DatePicker, Tag, message, Card, Typography } from 'antd';\n`;
      content = content.slice(0, insertPosition) + antdImports + content.slice(insertPosition);
    }
  }
  
  // Write the converted content back to file
  fs.writeFileSync(filePath, content, 'utf8');
}

// Run standardization if called directly
if (require.main === module) {
  standardizeComponents()
    .then(() => {
      console.log('✅ Component standardization completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Component standardization failed:', error);
      process.exit(1);
    });
}

export { standardizeComponents };
