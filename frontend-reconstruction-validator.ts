/**
 * FRONTEND RECONSTRUCTION VALIDATOR
 * Validates that ALL admin components meet production standards
 */

import fs from 'fs';
import path from 'path';

interface ValidationReport {
  totalFiles: number;
  validFiles: number;
  invalidFiles: string[];
  heroiconsFound: string[];
  missingAntdImports: string[];
  inconsistentPatterns: string[];
  recommendations: string[];
}

async function validateFrontendReconstruction(): Promise<ValidationReport> {
  console.log('🔍 FRONTEND RECONSTRUCTION VALIDATION');
  console.log('🚨 PRODUCTION STANDARDS ENFORCEMENT');
  console.log('=' .repeat(70));

  const report: ValidationReport = {
    totalFiles: 0,
    validFiles: 0,
    invalidFiles: [],
    heroiconsFound: [],
    missingAntdImports: [],
    inconsistentPatterns: [],
    recommendations: []
  };

  const adminDirs = [
    path.join(__dirname, 'src/admin'),
    path.join(__dirname, 'src/components/admin')
  ];

  for (const adminDir of adminDirs) {
    if (fs.existsSync(adminDir)) {
      await validateDirectory(adminDir, report);
    }
  }

  // Generate recommendations
  generateRecommendations(report);

  console.log('=' .repeat(70));
  console.log('📊 VALIDATION RESULTS');
  console.log(`📁 Total files: ${report.totalFiles}`);
  console.log(`✅ Valid files: ${report.validFiles}`);
  console.log(`❌ Invalid files: ${report.invalidFiles.length}`);
  console.log(`🚨 Heroicons found: ${report.heroiconsFound.length}`);
  console.log(`⚠️ Missing Ant Design: ${report.missingAntdImports.length}`);
  console.log(`🔧 Recommendations: ${report.recommendations.length}`);
  console.log('=' .repeat(70));

  if (report.invalidFiles.length === 0 && report.heroiconsFound.length === 0) {
    console.log('🎉 FRONTEND RECONSTRUCTION: 100% COMPLIANT');
    console.log('✅ ALL COMPONENTS MEET PRODUCTION STANDARDS');
  } else {
    console.log('🚨 FRONTEND RECONSTRUCTION: ISSUES DETECTED');
    console.log('❌ IMMEDIATE ACTION REQUIRED');
  }

  return report;
}

async function validateDirectory(dir: string, report: ValidationReport): Promise<void> {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      await validateDirectory(fullPath, report);
    } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
      report.totalFiles++;
      await validateFile(fullPath, report);
    }
  }
}

async function validateFile(filePath: string, report: ValidationReport): Promise<void> {
  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath);
  let isValid = true;

  console.log(`🔍 Validating: ${fileName}`);

  // Check for Heroicons contamination
  if (content.includes('@heroicons/react') || content.includes('heroicons')) {
    report.heroiconsFound.push(filePath);
    isValid = false;
    console.log(`❌ ${fileName}: Heroicons contamination detected`);
  }

  // Check for Ant Design imports
  if (!content.includes("from 'antd'") && !content.includes('from "antd"')) {
    // Check if file actually needs Ant Design (has UI components)
    const hasUIComponents = /Button|Table|Modal|Form|Input|Select|Card/.test(content);
    if (hasUIComponents) {
      report.missingAntdImports.push(filePath);
      isValid = false;
      console.log(`⚠️ ${fileName}: Missing Ant Design imports`);
    }
  }

  // Check for consistent patterns
  const inconsistencies = checkPatternConsistency(content, fileName);
  if (inconsistencies.length > 0) {
    report.inconsistentPatterns.push(...inconsistencies.map(inc => `${fileName}: ${inc}`));
    isValid = false;
  }

  if (isValid) {
    report.validFiles++;
    console.log(`✅ ${fileName}: Valid`);
  } else {
    report.invalidFiles.push(filePath);
  }
}

function checkPatternConsistency(content: string, fileName: string): string[] {
  const inconsistencies: string[] = [];

  // Check for mixed icon libraries
  const hasAntdIcons = /@ant-design\/icons/.test(content);
  const hasHeroicons = /@heroicons\/react/.test(content);
  
  if (hasAntdIcons && hasHeroicons) {
    inconsistencies.push('Mixed icon libraries detected');
  }

  // Check for proper TypeScript interfaces
  if (!content.includes('interface ') && content.includes('useState') && fileName.includes('Manager')) {
    inconsistencies.push('Missing TypeScript interfaces for manager component');
  }

  // Check for proper error handling
  if (content.includes('fetch(') && !content.includes('catch')) {
    inconsistencies.push('Missing error handling for API calls');
  }

  // Check for proper loading states
  if (content.includes('useState') && !content.includes('loading')) {
    inconsistencies.push('Missing loading state management');
  }

  return inconsistencies;
}

function generateRecommendations(report: ValidationReport): void {
  if (report.heroiconsFound.length > 0) {
    report.recommendations.push('CRITICAL: Remove all Heroicons imports and replace with Ant Design icons');
  }

  if (report.missingAntdImports.length > 0) {
    report.recommendations.push('Add proper Ant Design imports to all UI components');
  }

  if (report.inconsistentPatterns.length > 0) {
    report.recommendations.push('Standardize component patterns across all admin files');
  }

  if (report.validFiles / report.totalFiles < 0.8) {
    report.recommendations.push('URGENT: Less than 80% of files meet standards - comprehensive refactoring required');
  }

  // Production standards recommendations
  report.recommendations.push('Implement comprehensive TypeScript interfaces for all components');
  report.recommendations.push('Add proper error boundaries and loading states');
  report.recommendations.push('Ensure consistent styling with Ant Design theme');
  report.recommendations.push('Add comprehensive prop validation');
  report.recommendations.push('Implement proper accessibility attributes');
}

// Run validation if called directly
if (require.main === module) {
  validateFrontendReconstruction()
    .then((report) => {
      console.log('✅ Frontend reconstruction validation completed');
      
      // Save detailed report
      const reportPath = path.join(__dirname, `frontend_validation_report_${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`📁 Detailed report saved: ${reportPath}`);
      
      process.exit(report.invalidFiles.length === 0 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Frontend reconstruction validation failed:', error);
      process.exit(1);
    });
}

export { validateFrontendReconstruction };
