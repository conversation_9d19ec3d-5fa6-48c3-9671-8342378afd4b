import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import Layout from './components/layout/Layout';
import EnhancedHome from './pages/EnhancedHome';
import Scholarships from './pages/Scholarships';
import Countries from './pages/Countries';
import CountryDetail from './pages/CountryDetail';
import ScholarshipsByLevel from './pages/ScholarshipsByLevel';
import UndergraduatePage from './pages/UndergraduatePage';
import MasterPage from './pages/MasterPage';
import DoctoratePage from './pages/DoctoratePage';
import Guides from './pages/Guides';
import Opportunities from './pages/Opportunities';
import TrainingPage from './pages/TrainingPage';
import InternshipPage from './pages/InternshipPage';
import ConferencePage from './pages/ConferencePage';
import WorkshopPage from './pages/WorkshopPage';
import CompetitionPage from './pages/CompetitionPage';
import OpportunityDetailPage from './pages/OpportunityDetailPage';
import OpportunitiesByType from './pages/OpportunitiesByType';
import About from './pages/About';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import EnhancedScholarshipDetailPage from './pages/EnhancedScholarshipDetailPage';
import { LanguageProvider } from './context/LanguageContext';
import { AuthProvider } from './contexts/AuthContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';

// Admin Components
import AdminLayout from './admin/components/AdminLayout';

import AdminLogin from './admin/pages/AdminLogin';
import LoginTest from './admin/pages/LoginTest';
import AdminDashboard from './admin/pages/AdminDashboard';
import ScholarshipManager from './components/admin/ScholarshipManager';
import MessagesManager from './components/admin/MessagesManager';
import NewsletterManager from './admin/components/NewsletterManager';
import AdminManagement from './admin/pages/AdminManagement';
import GuideManager from './admin/pages/GuideManager';
import OpportunityManager from './admin/pages/OpportunityManager';
import ContactManagement from './admin/pages/ContactManagement';
import Settings from './admin/components/Settings';
import ForgotPassword from './admin/pages/ForgotPassword';
import ResetPassword from './admin/pages/ResetPassword';
import TwoFactorSettings from './admin/pages/TwoFactorSettings';
import Analytics from './admin/pages/Analytics';
import EmailNotifications from './admin/pages/EmailNotifications';
import AccountRecovery from './pages/AccountRecovery';
import SecurityDashboard from './admin/pages/SecurityDashboard';

function App() {
  return (
    <HelmetProvider>
      <ErrorBoundary>
        <LanguageProvider>
          <AuthProvider>
              <Router>
                  <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<Layout />}>
                    <Route index element={<EnhancedHome />} />
                    <Route path="scholarships" element={<Scholarships />} />
                    <Route path="bourses" element={<Scholarships />} />
                    <Route path="licence" element={<UndergraduatePage />} />
                    <Route path="master" element={<MasterPage />} />
                    <Route path="doctorat" element={<DoctoratePage />} />
                    <Route path="scholarships/:id" element={<EnhancedScholarshipDetailPage />} />
                    <Route path="scholarships/level/:level" element={<ScholarshipsByLevel />} />
                    <Route path="bourse/:slug" element={<EnhancedScholarshipDetailPage />} />
                    <Route path="countries" element={<Countries />} />
                    <Route path="pays" element={<Countries />} />
                    <Route path="countries/:country" element={<CountryDetail />} />
                    <Route path="pays/:country" element={<CountryDetail />} />
                    <Route path="guides" element={<Guides />} />
                    <Route path="opportunities" element={<Opportunities />} />
                    <Route path="opportunities/type/:type" element={<OpportunitiesByType />} />
                    <Route path="formation" element={<TrainingPage />} />
                    <Route path="stage" element={<InternshipPage />} />
                    <Route path="conference" element={<ConferencePage />} />
                    <Route path="atelier" element={<WorkshopPage />} />
                    <Route path="concours" element={<CompetitionPage />} />
                    <Route path="opportunities/:id" element={<OpportunityDetailPage />} />
                    <Route path="opportunite/:slug" element={<OpportunityDetailPage />} />
                    <Route path="about" element={<About />} />
                    <Route path="contact" element={<Contact />} />
                    <Route path="*" element={<NotFound />} />
                  </Route>

                  {/* Admin Routes */}
                  <Route path="/admin" element={<ProtectedRoute><AdminLayout /></ProtectedRoute>}>
                    <Route index element={<Navigate to="dashboard" replace />} />
                    <Route path="dashboard" element={<AdminDashboard />} />
                    <Route path="analytics" element={<Analytics />} />
                    <Route path="scholarships" element={<ScholarshipManager />} />
                    <Route path="guides" element={<GuideManager />} />
                    <Route path="opportunities" element={<OpportunityManager />} />
                    <Route path="messages" element={<MessagesManager />} />
                    <Route path="contact-management" element={<ContactManagement />} />
                    <Route path="newsletter" element={<NewsletterManager />} />
                    <Route path="email-notifications" element={<EmailNotifications />} />
                    <Route path="admins" element={<ProtectedRoute requireMainAdmin><AdminManagement /></ProtectedRoute>} />
                    <Route path="security-dashboard" element={<ProtectedRoute requireMainAdmin><SecurityDashboard /></ProtectedRoute>} />
                    <Route path="settings" element={<Settings />} />
                    <Route path="security" element={<TwoFactorSettings />} />
                  </Route>
                  <Route path="/admin/login" element={<AdminLogin />} />
                  <Route path="/admin/login-test" element={<LoginTest />} />
                  <Route path="/admin/forgot-password" element={<ForgotPassword />} />
                  <Route path="/admin/reset-password/:token" element={<ResetPassword />} />
                  <Route path="/account-recovery" element={<AccountRecovery />} />
                  <Route path="/account-recovery/:accountType/:token" element={<AccountRecovery />} />

                  {/* Development routes can be added here if needed */}
                </Routes>
              </Router>
          </AuthProvider>
        </LanguageProvider>
      </ErrorBoundary>
    </HelmetProvider>
  );
}

export default App;