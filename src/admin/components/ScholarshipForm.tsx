import React, { useState, useEffect } from 'react';


interface ScholarshipFormProps {
  scholarship?: {
    id: number;
    title: string;
    description: string;
    thumbnail?: string;
    deadline: string;  // Changed from dateline to deadline to match backend
    isOpen: boolean;
    level?: string;
    country?: string;
    coverage?: string;
    financial_benefits_summary?: string;
    eligibility_summary?: string;
    scholarship_link?: string;
    youtube_link?: string;
    createdBy?: number;
    createdAt?: string;
    updatedAt?: string;
  } | null;
  onSubmit: (data: any) => void;
  onCancel: () => void;
}

const ScholarshipForm: React.FC<ScholarshipFormProps> = ({
  scholarship,
  onSubmit,
  onCancel,
}) => {

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    thumbnail: '',
    deadline: '',  // Changed from dateline to deadline
    isOpen: true,
    level: '',
    country: '',
    coverage: '',
    financial_benefits_summary: '',  // New field
    eligibility_summary: '',  // New field
    scholarship_link: '',  // New field
    youtube_link: '',  // Changed from youtubeLink object
  });

  // State to store the thumbnail preview
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);

  useEffect(() => {
    if (scholarship) {
      // Convert deadline to YYYY-MM-DD format for date input
      const deadlineDate = scholarship.deadline ?
        new Date(scholarship.deadline).toISOString().split('T')[0] : '';

      setFormData({
        ...scholarship,
        deadline: deadlineDate,
        // Ensure all fields exist with defaults if missing
        thumbnail: scholarship.thumbnail || '',
        level: scholarship.level || '',
        country: scholarship.country || '',
        coverage: scholarship.coverage || '',
        financial_benefits_summary: scholarship.financial_benefits_summary || '',
        eligibility_summary: scholarship.eligibility_summary || '',
        scholarship_link: scholarship.scholarship_link || '',
        youtube_link: scholarship.youtube_link || '',
      });

      // Set thumbnail preview if available
      if (scholarship.thumbnail) {
        // Check if it's a URL or base64 string
        if (scholarship.thumbnail.startsWith('data:image')) {
          setThumbnailPreview(scholarship.thumbnail);
        } else {
          // It's a URL, set it as the preview
          setThumbnailPreview(scholarship.thumbnail);
        }
      } else {
        setThumbnailPreview(null);
      }
    }
  }, [scholarship]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  // All the array handling functions have been removed as we're now using simple string fields

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Thumbnail is optional, so we don't need to validate it
    // Just submit the form data with the thumbnail (either base64 or URL)
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-h-[80vh] overflow-y-auto p-4">
      {/* Basic Information Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Basic Information</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              Scholarship Title
            </label>
            <input
              type="text"
              name="title"
              id="title"
              value={formData.title}
              onChange={handleChange}
              required
              placeholder="e.g., Government of Canada Scholarship 2025"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="level" className="block text-sm font-medium text-gray-700">
              Study Level
            </label>
            <select
              name="level"
              id="level"
              value={formData.level}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            >
              <option value="">Select Level</option>
              <option value="Bachelor">Bachelor</option>
              <option value="Master">Master</option>
              <option value="PhD">PhD</option>
              <option value="PostDoc">PostDoc</option>
            </select>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Detailed Description
            </label>
            <textarea
              name="description"
              id="description"
              rows={4}
              value={formData.description}
              onChange={handleChange}
              required
              placeholder="Describe the scholarship program, objectives, and target audience..."
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="coverage" className="block text-sm font-medium text-gray-700">
              Scholarship Coverage
            </label>
            <select
              name="coverage"
              id="coverage"
              value={formData.coverage}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            >
              <option value="">Select Coverage</option>
              <option value="Fully Funded">Fully Funded</option>
              <option value="Partially Funded">Partially Funded</option>
            </select>
          </div>

          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-700">
              Country
            </label>
            <input
              type="text"
              name="country"
              id="country"
              value={formData.country}
              onChange={handleChange}
              required
              placeholder="e.g., Canada, Germany, Japan"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>

          <div>
            <label htmlFor="deadline" className="block text-sm font-medium text-gray-700">
              Application Deadline
            </label>
            <input
              type="date"
              name="deadline"
              id="deadline"
              value={formData.deadline}
              onChange={handleChange}
              required
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Financial Benefits Summary Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Financial Benefits</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="financial_benefits_summary" className="block text-sm font-medium text-gray-700">
              Financial Benefits Summary
            </label>
            <textarea
              name="financial_benefits_summary"
              id="financial_benefits_summary"
              rows={4}
              value={formData.financial_benefits_summary}
              onChange={handleChange}
              placeholder="e.g., Tuition fees, Monthly stipend, Health insurance, Travel allowance"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* These sections have been removed as they're not in our new schema */}

      {/* Eligibility Summary Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Eligibility Criteria</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="eligibility_summary" className="block text-sm font-medium text-gray-700">
              Eligibility Summary
            </label>
            <textarea
              name="eligibility_summary"
              id="eligibility_summary"
              rows={4}
              value={formData.eligibility_summary}
              onChange={handleChange}
              placeholder="e.g., Under 35 years old, Bachelor's degree holder, English proficiency"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Required Documents Section removed as it's not in our new schema */}

      {/* Scholarship Link Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Official Scholarship Link</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="scholarship_link" className="block text-sm font-medium text-gray-700">
              Scholarship Website URL
            </label>
            <input
              type="url"
              name="scholarship_link"
              id="scholarship_link"
              value={formData.scholarship_link}
              onChange={handleChange}
              placeholder="https://example.com/scholarship"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* YouTube Link Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">YouTube Presentation (Optional)</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="youtube_link" className="block text-sm font-medium text-gray-700">
              YouTube Video URL
            </label>
            <input
              type="url"
              name="youtube_link"
              id="youtube_link"
              value={formData.youtube_link}
              onChange={handleChange}
              placeholder="https://www.youtube.com/watch?v=example"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>
        </div>
      </div>

      {/* Thumbnail Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Thumbnail Image</h2>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm font-medium text-gray-700">
              Thumbnail Status:
              <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${formData.thumbnail ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {formData.thumbnail ? 'Image Selected' : 'No Image Selected'}
              </span>
            </div>
          </div>

          <div>
            <input
              type="file"
              accept="image/jpeg,image/png,image/jpg"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  // Validate file size (max 5MB)
                  if (file.size > 5 * 1024 * 1024) {
                    alert('Image file is too large. Maximum size is 5MB.');
                    return;
                  }

                  const reader = new FileReader();
                  reader.onloadend = () => {
                    const result = reader.result as string;
                    setFormData({ ...formData, thumbnail: result });
                    setThumbnailPreview(result);
                  };
                  reader.readAsDataURL(file);
                }
              }}
              className="mt-1 block w-full text-sm text-gray-500
                file:mr-4 file:py-2 file:px-4
                file:rounded-md file:border-0
                file:text-sm file:font-semibold
                file:bg-primary/10 file:text-primary
                hover:file:bg-primary/20"
            />
            <p className="mt-1 text-sm text-gray-500">
              Accepted formats: .jpg, .jpeg, .png - Maximum size: 5MB
            </p>
          </div>

          {/* Thumbnail Preview */}
          {thumbnailPreview && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-700 mb-2">Preview:</p>
              <div className="relative w-40 h-40 border rounded-md overflow-hidden">
                <img
                  src={thumbnailPreview}
                  alt="Thumbnail preview"
                  className="w-full h-full object-cover"
                />
                <button
                  type="button"
                  onClick={() => {
                    setFormData({ ...formData, thumbnail: '' });
                    setThumbnailPreview(null);
                  }}
                  className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  title="Remove image"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* URL Input Option */}
          <div className="mt-4">
            <p className="text-sm font-medium text-gray-700 mb-2">Or enter image URL:</p>
            <input
              type="url"
              name="thumbnail"
              value={formData.thumbnail.startsWith('data:image') ? '' : formData.thumbnail}
              onChange={(e) => {
                const url = e.target.value;
                setFormData({ ...formData, thumbnail: url });
                if (url) {
                  setThumbnailPreview(url);
                } else {
                  setThumbnailPreview(null);
                }
              }}
              placeholder="https://example.com/image.jpg"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
            />
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-700">
              <strong>Note:</strong> Thumbnails are important for making scholarships visually appealing to users.
              They will appear on the scholarship cards in the public portal.
            </p>
          </div>
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90"
        >
          {scholarship ? 'Update Scholarship' : 'Create Scholarship'}
        </button>
      </div>
    </form>
  );
};

export default ScholarshipForm;