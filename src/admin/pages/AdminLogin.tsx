import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Input, Button, Card, Alert, Typography, Spin, Progress } from 'antd';
import { UserOutlined, LockOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';

const { Title, Text } = Typography;

interface LoginFormData {
  email: string;
  password: string;
}

interface RateLimitInfo {
  isRateLimited: boolean;
  retryAfter: number;
  message: string;
}

const AdminLogin: React.FC = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, isLoading, error, clearError } = useAuth();
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rateLimitInfo, setRateLimitInfo] = useState<RateLimitInfo>({
    isRateLimited: false,
    retryAfter: 0,
    message: ''
  });
  const [countdown, setCountdown] = useState(0);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      // Add a small delay to prevent navigation conflicts
      const timer = setTimeout(() => {
        navigate('/admin/dashboard', { replace: true });
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, isLoading, navigate]);

  // Clear errors when component mounts
  useEffect(() => {
    clearError();
  }, [clearError]);

  // Countdown timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    } else if (rateLimitInfo.isRateLimited && countdown === 0) {
      // Rate limit period has ended
      setRateLimitInfo({
        isRateLimited: false,
        retryAfter: 0,
        message: ''
      });
    }
    return () => clearTimeout(timer);
  }, [countdown, rateLimitInfo.isRateLimited]);

  // Parse rate limit error and start countdown
  const handleRateLimitError = useCallback((errorMessage: string, retryAfter?: number) => {
    const retrySeconds = retryAfter || 900; // Default to 15 minutes
    setRateLimitInfo({
      isRateLimited: true,
      retryAfter: retrySeconds,
      message: errorMessage
    });
    setCountdown(retrySeconds);
  }, []);

  // Reset rate limit for development
  const handleResetRateLimit = async () => {
    try {
      const response = await fetch('/api/auth/reset-rate-limit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setRateLimitInfo({
          isRateLimited: false,
          retryAfter: 0,
          message: ''
        });
        setCountdown(0);
        clearError();
        alert('Rate limit reset successfully! You can now try logging in again.');
      } else {
        const errorData = await response.json();
        alert('Failed to reset rate limit: ' + errorData.message);
      }
    } catch (error: any) {
      alert('Error resetting rate limit: ' + error.message);
    }
  };

  // Handle form submission
  const handleSubmit = async (values: LoginFormData) => {
    try {
      setIsSubmitting(true);
      console.log('🔐 AdminLogin: Starting login process...');

      await login(values.email, values.password);
      console.log('✅ AdminLogin: Login successful, waiting for navigation...');
      // Navigation will be handled by the useEffect above
    } catch (error: any) {
      // Check if it's a rate limit error
      if ((error.message && error.message.includes('rate limit')) ||
          (error.message && error.message.includes('too many'))) {
        // Try to extract retry time from error message
        const retryMatch = error.message.match(/(\d+)/);
        const retryAfter = retryMatch ? parseInt(retryMatch[1]) : 900;
        handleRateLimitError(error.message, retryAfter);
      }
      // Error is handled by the auth context
      console.error('❌ AdminLogin: Login failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Debug functions
  const handleClearCookies = async () => {
    try {
      const authService = (await import('../../services/authService')).default;
      await authService.clearAllCookies();
      alert('Cookies cleared! Please refresh the page.');
      window.location.reload();
    } catch (error: any) {
      alert('Error clearing cookies: ' + error.message);
    }
  };

  const handleResetAccount = async () => {
    try {
      const authService = (await import('../../services/authService')).default;
      await authService.resetAdminAccount('<EMAIL>');
      alert('Admin account reset! You can now try logging in.');
    } catch (error: any) {
      alert('Error resetting account: ' + error.message);
    }
  };

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Spin size="large" tip="Loading..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Title level={2} className="text-gray-900">
            MaBourse Admin Portal
          </Title>
          <Text className="text-gray-600">
            Sign in to access the admin dashboard
          </Text>
        </div>

        <Card className="shadow-lg">
          <Form
            form={form}
            name="admin-login"
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
            autoComplete="off"
          >
            {error && !rateLimitInfo.isRateLimited && (
              <Alert
                message="Login Failed"
                description={error}
                type="error"
                showIcon
                closable
                onClose={clearError}
                className="mb-4"
              />
            )}

            {rateLimitInfo.isRateLimited && (
              <Alert
                message="Rate Limit Exceeded"
                description={
                  <div className="space-y-3">
                    <div>{rateLimitInfo.message}</div>
                    <div className="flex items-center space-x-2">
                      <ClockCircleOutlined />
                      <span>
                        Please wait {Math.floor(countdown / 60)}:{(countdown % 60).toString().padStart(2, '0')}
                        {' '}before trying again
                      </span>
                    </div>
                    <Progress
                      percent={Math.max(0, 100 - (countdown / rateLimitInfo.retryAfter) * 100)}
                      size="small"
                      status="active"
                      showInfo={false}
                    />
                    {process.env.NODE_ENV === 'development' && (
                      <Button
                        size="small"
                        type="link"
                        onClick={handleResetRateLimit}
                        className="p-0"
                      >
                        Reset Rate Limit (Dev Only)
                      </Button>
                    )}
                  </div>
                }
                type="warning"
                showIcon
                className="mb-4"
              />
            )}

            <Form.Item
              name="email"
              label="Email Address"
              rules={[
                { required: true, message: 'Please enter your email address' },
                { type: 'email', message: 'Please enter a valid email address' }
              ]}
            >
              <Input
                prefix={<UserOutlined className="text-gray-400" />}
                placeholder="<EMAIL>"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item
              name="password"
              label="Password"
              rules={[
                { required: true, message: 'Please enter your password' }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined className="text-gray-400" />}
                placeholder="Enter your password"
                autoComplete="current-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isSubmitting}
                disabled={isSubmitting || rateLimitInfo.isRateLimited}
                className="w-full h-12 text-lg font-medium"
              >
                {isSubmitting
                  ? 'Signing In...'
                  : rateLimitInfo.isRateLimited
                    ? `Rate Limited (${Math.floor(countdown / 60)}:${(countdown % 60).toString().padStart(2, '0')})`
                    : 'Sign In'
                }
              </Button>
            </Form.Item>
          </Form>

          <div className="text-center mt-4">
            <Text className="text-gray-500 text-sm">
              Secure authentication with HTTP-only cookies
            </Text>
          </div>

          {/* Debug buttons - remove in production */}
          <div className="mt-4 space-y-2">
            <Button
              type="default"
              size="small"
              onClick={handleClearCookies}
              className="w-full"
            >
              Clear All Cookies
            </Button>
            <Button
              type="default"
              size="small"
              onClick={handleResetAccount}
              className="w-full"
            >
              Reset Admin Account
            </Button>
          </div>
        </Card>

        <div className="text-center">
          <Text className="text-gray-400 text-xs">
            © 2025 MaBourse. All rights reserved.
          </Text>
        </div>
      </div>
    </div>
  );
};

export default AdminLogin;
