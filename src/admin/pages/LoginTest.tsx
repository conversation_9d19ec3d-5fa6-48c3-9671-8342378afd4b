import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Input, Alert } from 'antd';

const LoginTest: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testLogin = async () => {
    setLoading(true);
    setResult(null);

    try {
      console.log('🔐 Testing login with fetch API...');

      // First, reset rate limiting
      await fetch('http://localhost:5000/api/auth/reset-admin-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email })
      });

      // Test login with fetch API (same as axios internally)
      const loginResponse = await fetch('http://localhost:5000/api/auth/admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // This is crucial for cookies
        body: JSON.stringify({ email, password })
      });

      const loginData = await loginResponse.json();
      console.log('✅ Login response:', loginData);

      if (loginData.success) {
        // Small delay to ensure cookie is set
        await new Promise(resolve => setTimeout(resolve, 100));

        // Test authenticated request
        console.log('🔍 Testing authenticated request...');

        const profileResponse = await fetch('http://localhost:5000/api/admin/me', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include' // This is crucial for cookies
        });

        const profileData = await profileResponse.json();
        console.log('✅ Profile response:', profileData);

        setResult({
          login: loginData,
          profile: profileData,
          success: profileResponse.ok,
          cookieTest: 'Cookies working properly!'
        });
      } else {
        setResult({
          login: loginData,
          error: 'Login failed'
        });
      }
    } catch (error: any) {
      console.error('❌ Test failed:', error);
      setResult({
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const testDebug = async () => {
    try {
      console.log('🔍 Testing debug endpoint...');

      const response = await fetch('http://localhost:5000/api/auth/debug', {
        method: 'GET',
        credentials: 'include'
      });

      const data = await response.json();
      console.log('🔍 Debug response:', data);

      setResult({
        debug: data
      });
    } catch (error: any) {
      console.error('❌ Debug test failed:', error);
      setResult({
        error: error.message
      });
    }
  };

  const resetRateLimit = async () => {
    try {
      console.log('🔄 Resetting rate limit...');

      const response = await fetch('http://localhost:5000/api/auth/reset-admin-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email })
      });

      const data = await response.json();
      console.log('✅ Rate limit reset:', data);

      setResult({
        resetResult: data
      });
    } catch (error: any) {
      console.error('❌ Rate limit reset failed:', error);
      setResult({
        error: error.message
      });
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <Card title="🔐 Authentication Test Tool" style={{ marginBottom: '20px' }}>
        <div style={{ marginBottom: '16px' }}>
          <label>Email:</label>
          <Input 
            value={email} 
            onChange={(e) => setEmail(e.target.value)}
            style={{ marginTop: '4px' }}
          />
        </div>
        
        <div style={{ marginBottom: '16px' }}>
          <label>Password:</label>
          <Input.Password 
            value={password} 
            onChange={(e) => setPassword(e.target.value)}
            style={{ marginTop: '4px' }}
          />
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Button 
            type="primary" 
            onClick={testLogin} 
            loading={loading}
            style={{ marginRight: '8px' }}
          >
            Test Login + Profile
          </Button>
          
          <Button
            onClick={testDebug}
            style={{ marginRight: '8px' }}
          >
            Test Debug
          </Button>

          <Button
            onClick={resetRateLimit}
            type="default"
            danger
          >
            Reset Rate Limit
          </Button>
        </div>

        {result && (
          <Alert
            message="Test Results"
            description={
              <pre style={{ fontSize: '12px', maxHeight: '400px', overflow: 'auto' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            }
            type={result.success ? 'success' : result.error ? 'error' : 'info'}
            showIcon
          />
        )}
      </Card>

      <Card title="📋 Instructions">
        <ol>
          <li>Open browser Developer Tools (F12)</li>
          <li>Go to Console tab to see detailed logs</li>
          <li>Go to Network tab to see HTTP requests</li>
          <li>Go to Application/Storage tab to see cookies</li>
          <li>Click "Test Login + Profile" to test the full flow</li>
          <li>Check if cookies are being set and sent properly</li>
        </ol>
      </Card>
    </div>
  );
};

export default LoginTest;
