import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  message,
  Card,
  Typography,
  Tag
} from 'antd';
import { MailOutlined } from '@ant-design/icons';
import authService from '../../services/authService';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface Message {
  id: number;
  name: string;
  email: string;
  subject: string;
  content: string;
  status: string;
  createdAt: string;
}

const Messages: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isReplyModalVisible, setIsReplyModalVisible] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    fetchMessages();
  }, []);

  const fetchMessages = async () => {
    try {
      const data = await authService.request('GET', '/messages');
      setMessages(data);
    } catch (error) {
      message.error('Failed to fetch messages');
    }
  };

  const handleReply = async (values: { reply: string }) => {
    if (!selectedMessage) return;

    try {
      await authService.request('POST', `/messages/${selectedMessage.id}/reply`, { reply: values.reply });
      message.success('Reply sent successfully');
      setIsReplyModalVisible(false);
      form.resetFields();
      fetchMessages();
    } catch (error) {
      message.error('Failed to send reply');
    }
  };

  const columns = [
    {
      title: 'From',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Message) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-gray-500 text-sm">{record.email}</div>
        </div>
      ),
    },
    {
      title: 'Subject',
      dataIndex: 'subject',
      key: 'subject',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'replied' ? 'green' : 'blue'}>
          {status === 'replied' ? 'Replied' : 'Pending'}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: any, record: Message) => (
        <Button
          type="primary"
          icon={<MailOutlined />}
          onClick={() => {
            setSelectedMessage(record);
            setIsReplyModalVisible(true);
          }}
        >
          Reply
        </Button>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Card>
        <div className="flex justify-between items-center mb-6">
          <Title level={2}>Messages</Title>
        </div>

        <Table
          columns={columns}
          dataSource={messages}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          expandable={{
            expandedRowRender: (record: Message) => (
              <div className="p-4 bg-gray-50 rounded">
                <Text>{record.content}</Text>
              </div>
            ),
          }}
        />
      </Card>

      <Modal
        title={`Reply to ${selectedMessage?.name}`}
        open={isReplyModalVisible}
        onCancel={() => setIsReplyModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleReply}
        >
          <Form.Item
            name="reply"
            rules={[{ required: true, message: 'Please input your reply!' }]}
          >
            <TextArea rows={6} placeholder="Type your reply here..." />
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" block>
              Send Reply
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Messages;