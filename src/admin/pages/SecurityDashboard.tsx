import React, { useState, useEffect, useCallback } from 'react';
import { Card, Table, Tag, Typography, Statistic, Row, Col, DatePicker, Button, Space, Alert, Spin } from 'antd';
import {
  SecurityScanOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import authService from '../../services/authService';
import { format } from 'date-fns';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

// Define security event types
enum SecurityEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  PASSWORD_RESET = 'PASSWORD_RESET',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  TWO_FACTOR_SUCCESS = 'TWO_FACTOR_SUCCESS',
  TWO_FACTOR_FAILURE = 'TWO_FACTOR_FAILURE',
}

// Define security event interface
interface SecurityEvent {
  id: number;
  eventType: SecurityEventType;
  message: string;
  userId?: number;
  email?: string;
  ip: string;
  userAgent?: string;
  timestamp: string;
  details?: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

// Define security stats interface
interface SecurityStats {
  totalEvents: number;
  successfulLogins: number;
  failedLogins: number;
  suspiciousActivities: number;
  accountLockouts: number;
  twoFactorSuccesses: number;
  twoFactorFailures: number;
}

const SecurityDashboard: React.FC = () => {
  const { admin } = useAuth();
  const isMainAdmin = admin?.isMainAdmin || false;
  const [loading, setLoading] = useState<boolean>(true);
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [stats, setStats] = useState<SecurityStats>({
    totalEvents: 0,
    successfulLogins: 0,
    failedLogins: 0,
    suspiciousActivities: 0,
    accountLockouts: 0,
    twoFactorSuccesses: 0,
    twoFactorFailures: 0,
  });
  const [dateRange, setDateRange] = useState<[Date, Date] | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch security events
  const fetchSecurityEvents = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Prepare date range parameters
      const params: Record<string, string> = {};
      if (dateRange) {
        params.startDate = dateRange[0].toISOString();
        params.endDate = dateRange[1].toISOString();
      }

      // Fetch events from API
      const response = await authService.request('GET', '/admin/security/events', params);

      if (response.success && response.data) {
        setEvents(response.data.events);
        setStats(response.data.stats);
      } else {
        setError(response.message || 'Failed to fetch security events');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  }, [dateRange]);

  // Initial data fetch
  useEffect(() => {
    fetchSecurityEvents();
  }, [fetchSecurityEvents]);

  // Handle date range change
  const handleDateRangeChange = (dates: any) => {
    if (dates) {
      setDateRange([dates[0].toDate(), dates[1].toDate()]);
    } else {
      setDateRange(null);
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    fetchSecurityEvents();
  };

  // Get tag color based on event type
  const getEventTypeTag = (eventType: SecurityEventType) => {
    switch (eventType) {
      case SecurityEventType.LOGIN_SUCCESS:
        return <Tag color="success" icon={<CheckCircleOutlined />}>Login Success</Tag>;
      case SecurityEventType.LOGIN_FAILURE:
        return <Tag color="error" icon={<CloseCircleOutlined />}>Login Failure</Tag>;
      case SecurityEventType.LOGOUT:
        return <Tag color="default">Logout</Tag>;
      case SecurityEventType.PASSWORD_RESET:
        return <Tag color="processing">Password Reset</Tag>;
      case SecurityEventType.ACCOUNT_LOCKED:
        return <Tag color="error" icon={<ExclamationCircleOutlined />}>Account Locked</Tag>;
      case SecurityEventType.SUSPICIOUS_ACTIVITY:
        return <Tag color="warning" icon={<WarningOutlined />}>Suspicious Activity</Tag>;
      case SecurityEventType.TWO_FACTOR_SUCCESS:
        return <Tag color="success">2FA Success</Tag>;
      case SecurityEventType.TWO_FACTOR_FAILURE:
        return <Tag color="error">2FA Failure</Tag>;
      default:
        return <Tag>Unknown</Tag>;
    }
  };

  // Get severity tag
  const getSeverityTag = (severity: string) => {
    switch (severity) {
      case 'info':
        return <Tag color="blue">Info</Tag>;
      case 'warning':
        return <Tag color="orange">Warning</Tag>;
      case 'error':
        return <Tag color="red">Error</Tag>;
      case 'critical':
        return <Tag color="volcano" icon={<WarningOutlined />}>Critical</Tag>;
      default:
        return <Tag>Unknown</Tag>;
    }
  };

  // Table columns
  const columns = [
    {
      title: 'Time',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => format(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      sorter: (a: SecurityEvent, b: SecurityEvent) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    },
    {
      title: 'Event',
      dataIndex: 'eventType',
      key: 'eventType',
      render: (text: SecurityEventType) => getEventTypeTag(text),
      filters: Object.values(SecurityEventType).map(type => ({ text: type, value: type })),
      onFilter: (value: any, record: SecurityEvent) => record.eventType === value.toString(),
    },
    {
      title: 'User',
      dataIndex: 'email',
      key: 'email',
      render: (text: string, record: SecurityEvent) => text || 'N/A',
    },
    {
      title: 'IP Address',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: 'Severity',
      dataIndex: 'severity',
      key: 'severity',
      render: (text: string) => getSeverityTag(text),
      filters: [
        { text: 'Info', value: 'info' },
        { text: 'Warning', value: 'warning' },
        { text: 'Error', value: 'error' },
        { text: 'Critical', value: 'critical' },
      ],
      onFilter: (value: any, record: SecurityEvent) => record.severity === value.toString(),
    },
    {
      title: 'Message',
      dataIndex: 'message',
      key: 'message',
    },
  ];

  return (
    <div className="p-6">
      <Title level={2}>
        <SecurityScanOutlined /> Security Dashboard
      </Title>

      <Text className="mb-6 block text-gray-600">
        Monitor security events and detect potential threats to your system.
      </Text>

      {!isMainAdmin && (
        <Alert
          message="Limited Access"
          description="You have limited access to security information. Contact the main administrator for full access."
          type="warning"
          showIcon
          className="mb-6"
        />
      )}

      {/* Security Stats */}
      <Card className="mb-6 shadow-md">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Statistic
              title="Total Events"
              value={stats.totalEvents}
              prefix={<SecurityScanOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Statistic
              title="Successful Logins"
              value={stats.successfulLogins}
              valueStyle={{ color: '#3f8600' }}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Statistic
              title="Failed Logins"
              value={stats.failedLogins}
              valueStyle={{ color: '#cf1322' }}
              prefix={<CloseCircleOutlined />}
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Statistic
              title="Suspicious Activities"
              value={stats.suspiciousActivities}
              valueStyle={{ color: '#faad14' }}
              prefix={<WarningOutlined />}
            />
          </Col>
        </Row>
      </Card>

      {/* Filters and Controls */}
      <Card className="mb-6 shadow-md">
        <Space direction="horizontal" size="middle" className="mb-4">
          <RangePicker onChange={handleDateRangeChange} />
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            Refresh
          </Button>
        </Space>

        {error && (
          <Alert
            message="Error"
            description={error}
            type="error"
            showIcon
            closable
            onClose={() => setError(null)}
            className="mb-4"
          />
        )}
      </Card>

      {/* Security Events Table */}
      <Card className="shadow-md">
        <Title level={4}>Security Events</Title>
        <Spin spinning={loading}>
          <Table
            dataSource={events}
            columns={columns}
            rowKey="id"
            pagination={{ pageSize: 10 }}
            expandable={{
              expandedRowRender: (record) => (
                <div className="p-4 bg-gray-50">
                  <p><strong>User Agent:</strong> {record.userAgent || 'N/A'}</p>
                  {record.details && <p><strong>Details:</strong> {record.details}</p>}
                </div>
              ),
            }}
          />
        </Spin>
      </Card>
    </div>
  );
};

export default SecurityDashboard;
