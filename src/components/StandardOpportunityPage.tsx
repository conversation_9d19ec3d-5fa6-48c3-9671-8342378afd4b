import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import EnhancedOpportunityCard from './EnhancedOpportunityCard';
import GreatYOPPagination from './GreatYOPPagination';
import CommentsSection from './CommentsSection';
import ProfessionalNewsletterSection from './ProfessionalNewsletterSection';
import AdPlacement from './AdPlacement';
import { Spin, Alert } from 'antd';
import { getOpportunityImage } from '../utils/opportunityImageUtils';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
  slug?: string;
}

interface StandardOpportunityPageConfig {
  type: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroDescription: string;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
}

interface StandardOpportunityPageProps {
  config: StandardOpportunityPageConfig;
}

const StandardOpportunityPage: React.FC<StandardOpportunityPageProps> = ({ config }) => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 6,
    total: 0
  });

  const handleOpportunityClick = (id: number, slug?: string) => {
    // Navigate to opportunity details page using slug if available (same pattern as scholarships)
    if (slug) {
      window.location.href = `/opportunite/${slug}`;
    } else {
      window.location.href = `/opportunities/${id}`;
    }
  };

  // Helper function to convert opportunity type to display name (like scholarship levels)
  const getTypeDisplayName = (type: string): string => {
    const typeMap: Record<string, string> = {
      'internship': 'Stage',
      'training': 'Formation',
      'conference': 'Conférence',
      'workshop': 'Atelier',
      'competition': 'Concours'
    };
    return typeMap[type] || type;
  };

  const fetchOpportunities = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await fetch(`${apiUrl}/api/opportunities/type/${encodeURIComponent(config.type)}?page=${pagination.page}&limit=${pagination.limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        // Handle new response structure with nested data and pagination
        const responseData = data.data;
        if (responseData && responseData.data) {
          setOpportunities(responseData.data || []);
          setPagination(prev => ({
            ...prev,
            total: responseData.pagination?.total || 0,
            totalPages: responseData.pagination?.totalPages || 1,
            hasNextPage: responseData.pagination?.hasNextPage || false,
            hasPreviousPage: responseData.pagination?.hasPreviousPage || false
          }));
        } else {
          // Fallback for direct array response
          setOpportunities(data.data || []);
          setPagination(prev => ({
            ...prev,
            total: data.data?.length || 0
          }));
        }
      } else {
        throw new Error(data.message || 'Failed to fetch opportunities');
      }
    } catch (error) {
      console.error('Error fetching opportunities:', error);
      setError('Impossible de charger les opportunités. Veuillez réessayer plus tard.');
      setOpportunities([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOpportunities();
  }, [pagination.page, config.type]); // eslint-disable-line react-hooks/exhaustive-deps

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };



  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      {/* Hero Section - Reduced Height for Professional Look */}
      <section className="bg-white pt-20 pb-1" style={{ paddingTop: '4rem' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-0">
            <span className="text-3xl mr-3">💼</span>
            <h1 style={{
              fontSize: '24px',
              marginBottom: 0,
              color: '#2563eb',
              fontWeight: 700,
              textTransform: 'capitalize'
            }}>
              {config.heroTitle}
            </h1>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="bg-white min-h-screen">
        {/* Information Section - Reduced Spacing */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-1 pb-4">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">{config.infoTitle}</h2>
            <p className="text-gray-700 text-lg leading-relaxed mb-6">{config.infoContent}</p>

            <div className="grid md:grid-cols-2 gap-6">
              {config.benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-primary rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-gray-700 leading-relaxed">{benefit}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Content Section - Full Width with Reduced Top Spacing */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 pb-16">
          {/* Opportunities Grid - Full Width 3x2 Layout */}
          <div id="opportunities-section" className="mb-8">
                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des opportunités..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    {/* Full Width 3x2 Grid Layout */}
                    <div className="gy-pcard-wrap country-page op-cards full-width-grid">
                      {opportunities.map((opportunity, index) => (
                        <EnhancedOpportunityCard
                          key={opportunity.id}
                          id={opportunity.id}
                          title={opportunity.title}
                          thumbnail={getOpportunityImage(opportunity.type, opportunity.thumbnail, index)}
                          deadline={opportunity.deadline}
                          isActive={opportunity.isActive}
                          onClick={(id, slug) => handleOpportunityClick(id, slug)}
                          type={opportunity.type}
                          organization={opportunity.organization}
                          location={opportunity.location}
                          isRemote={opportunity.isRemote}
                          featured={false}
                          index={index}
                          variant="greatyop"
                        />
                      ))}
                    </div>

                    {/* GreatYOP Pagination */}
                    {(pagination.total > pagination.limit || opportunities.length > 0) && (
                      <GreatYOPPagination
                        current={pagination.page}
                        total={Math.max(pagination.total, opportunities.length)}
                        pageSize={pagination.limit}
                        onChange={handlePageChange}
                        showQuickJumper={false}
                      />
                    )}
                  </>
                )}
          </div>

          {/* Desktop Ad - Full width leaderboard */}
          <div className="hidden lg:block py-8">
            <div className="text-center">
              <AdPlacement
                adSlot="9876543210"
                adSize="leaderboard"
                responsive={true}
              />
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <CommentsSection pageType="opportunity" pageId={config.type} />
        </div>

        {/* Professional Newsletter Section - After Comments */}
        <ProfessionalNewsletterSection type="opportunities" />
      </div>
    </>
  );
};

export default StandardOpportunityPage;
