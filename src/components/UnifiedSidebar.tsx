import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

interface Scholarship {
  id: number;
  title: string;
  thumbnail: string;
  slug?: string;
}

interface Opportunity {
  id: number;
  title: string;
  thumbnail?: string;
  type: string;
  organization: string;
  slug?: string;
}

interface UnifiedSidebarProps {
  config: {
    type: 'levels' | 'countries' | 'opportunities';
    currentItem: string;
    limit: number;
  };
  className?: string;
}

const UnifiedSidebar: React.FC<UnifiedSidebarProps> = ({ config, className = '' }) => {
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [email, setEmail] = useState('');
  const [subscribed, setSubscribed] = useState(false);

  const fetchData = async () => {
    try {
      setLoading(true);

      const params = new URLSearchParams({
        limit: (config.limit * 2).toString(), // Get more for different sections
        exclude: config.currentItem
      });

      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';

      if (config.type === 'opportunities') {
        // Fetch opportunities for sidebar
        const response = await fetch(`${apiUrl}/api/opportunities/latest?${params}`);

        if (!response.ok) {
          throw new Error('Failed to fetch opportunities');
        }

        const data = await response.json();

        if (data.success) {
          setOpportunities(data.data || []);
        }
      } else {
        // Fetch scholarships for sidebar
        const response = await fetch(`${apiUrl}/api/scholarships/latest?${params}`);

        if (!response.ok) {
          throw new Error('Failed to fetch scholarships');
        }

        const data = await response.json();

        if (data.success) {
          setScholarships(data.data || []);
        }
      }
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
      if (config.type === 'opportunities') {
        setOpportunities([]);
      } else {
        setScholarships([]);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [config.currentItem, config.limit, config.type]); // eslint-disable-line react-hooks/exhaustive-deps

  const getScholarshipUrl = (scholarship: Scholarship) => {
    if (scholarship.slug) {
      return `/bourse/${scholarship.slug}`;
    }
    return `/scholarships/${scholarship.id}`;
  };

  const getOpportunityUrl = (opportunity: Opportunity) => {
    if (opportunity.slug) {
      return `/opportunite/${opportunity.slug}`;
    }
    return `/opportunities/${opportunity.id}`;
  };

  const getTypeLabel = (type: string): string => {
    const labels = {
      internship: 'Stage',
      training: 'Formation',
      conference: 'Conférence',
      workshop: 'Atelier',
      competition: 'Concours'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
      const response = await fetch(`${apiUrl}/api/newsletter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        setSubscribed(true);
        setEmail('');
        setTimeout(() => setSubscribed(false), 3000);
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
    }
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-white rounded-xl shadow-lg p-6">
            <div className="animate-pulse space-y-4">
              <div className="h-6 bg-gray-200 rounded w-3/4"></div>
              <div className="space-y-3">
                {[...Array(3)].map((_, j) => (
                  <div key={j} className="flex items-center space-x-3">
                    <div className="h-12 w-16 bg-gray-200 rounded"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-full"></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Newsletter Subscription - Top Position */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h4 className="text-lg font-semibold text-gray-900 mb-2">
          Newsletter
        </h4>
        <p className="text-sm text-gray-600 mb-4">
          Recevez les dernières bourses directement dans votre boîte mail.
        </p>
        <form onSubmit={handleNewsletterSubmit} className="flex">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Votre email"
            className="flex-1 px-4 py-2 text-sm border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
          <button 
            type="submit"
            className={`px-4 py-2 text-sm font-medium rounded-r-lg transition-colors duration-200 ${
              subscribed 
                ? 'bg-green-600 text-white' 
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {subscribed ? 'Abonné!' : 'S\'abonner'}
          </button>
        </form>
      </div>



      {/* Latest Items - Enhanced 2x4 Card Layout */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {config.type === 'opportunities' ? 'Dernières Opportunités' : 'Dernières Bourses'}
          </h3>
          <div className="w-12 h-1 bg-green-600 rounded"></div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {config.type === 'opportunities' ? (
            opportunities.length > 0 ? (
              opportunities.slice(0, 8).map((opportunity) => (
                <Link
                  key={opportunity.id}
                  to={getOpportunityUrl(opportunity)}
                  className="block p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 group border border-gray-100 hover:border-primary/20"
                >
                  <div className="mb-2">
                    <img
                      src={opportunity.thumbnail || '/images/default-opportunity.jpg'}
                      alt={opportunity.title}
                      className="w-full h-16 object-cover rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/default-opportunity.jpg';
                      }}
                    />
                  </div>

                  <h4 className="text-xs font-medium text-gray-900 group-hover:text-primary transition-colors duration-200 line-clamp-2 mb-1">
                    {opportunity.title}
                  </h4>
                  <p className="text-xs text-gray-500 mb-1">
                    {getTypeLabel(opportunity.type)}
                  </p>
                  <p className="text-xs text-gray-400">
                    {opportunity.organization}
                  </p>
                </Link>
              ))
            ) : (
              <div className="col-span-2 text-center py-8">
                <div className="text-gray-400 mb-2">
                  <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-xs text-gray-500">Aucune opportunité disponible</p>
              </div>
            )
          ) : (
            scholarships.length > 0 ? (
              scholarships.slice(0, 8).map((scholarship) => (
                <Link
                  key={scholarship.id}
                  to={getScholarshipUrl(scholarship)}
                  className="block p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 group border border-gray-100 hover:border-primary/20"
                >
                  <div className="mb-2">
                    <img
                      src={scholarship.thumbnail || '/images/default-scholarship.jpg'}
                      alt={scholarship.title}
                      className="w-full h-16 object-cover rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/default-scholarship.jpg';
                      }}
                    />
                  </div>

                  <h4 className="text-xs font-medium text-gray-900 group-hover:text-primary transition-colors duration-200 line-clamp-2">
                    {scholarship.title}
                  </h4>
                </Link>
              ))
            ) : (
              <div className="col-span-2 text-center py-8">
                <div className="text-gray-400 mb-2">
                  <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <p className="text-xs text-gray-500">Aucune bourse disponible</p>
              </div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default UnifiedSidebar;
