import React, { useState, useEffect } from 'react';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  FilterOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { Button, Table, Space, Modal, Form, Input, Select, Switch, DatePicker, Tag, message, Card, Typography } from 'antd';
import ScholarshipForm from '../../admin/components/ScholarshipForm';
import adminApiService from '../../services/adminApiService';

const { Title } = Typography;

interface Scholarship {
  id: number;
  title: string;
  description: string;
  country: string;
  level: string;
  deadline: string;
  amount?: string;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financialBenefitsSummary?: string;
  eligibilitySummary?: string;
  scholarshipLink?: string;
  youtubeLink?: string;
  createdAt: string;
  updatedAt: string;
}

const ScholarshipManager: React.FC = () => {
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState('');
  const [filterCountry, setFilterCountry] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showBulkImportModal, setShowBulkImportModal] = useState(false);
  const [selectedScholarship, setSelectedScholarship] = useState<Scholarship | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bulkImportData, setBulkImportData] = useState('');

  useEffect(() => {
    fetchScholarships();
  }, []);

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      const response = await adminApiService.getScholarships();

      if (response.success && response.data) {
        setScholarships(response.data);
      } else {
        throw new Error('Failed to fetch scholarships');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateScholarship = async (formData: any) => {
    try {
      setIsSubmitting(true);

      // Prepare JSON data for submission (industry standard)
      const submitData = {
        title: formData.title,
        description: formData.description,
        amount: formData.amount ? parseFloat(formData.amount) : null,
        deadline: formData.deadline,
        eligibility: formData.eligibility || '',
        requirements: formData.requirements || '',
        application_url: formData.application_url || formData.scholarship_link || '',
        contact_email: formData.contact_email || '',
        organization: formData.organization || '',
        field_of_study: formData.field_of_study || '',
        level_of_study: formData.level_of_study || formData.level || '',
        country: formData.country || '',
        coverage: formData.coverage || '',
        financial_benefits_summary: formData.financial_benefits_summary || '',
        eligibility_summary: formData.eligibility_summary || '',
        scholarship_link: formData.scholarship_link || '',
        youtube_link: formData.youtube_link || '',
        thumbnail: formData.thumbnail || '',
        is_active: formData.isOpen !== false
      };

      const response = await adminApiService.createScholarship(submitData);

      if (!response.success) {
        throw new Error(response.message || 'Failed to create scholarship');
      }

      await fetchScholarships();
      setShowAddModal(false);
      alert('Scholarship created successfully!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to create scholarship');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUpdateScholarship = async (formData: any) => {
    if (!selectedScholarship) return;

    try {
      setIsSubmitting(true);

      // Prepare JSON data for submission (industry standard)
      const submitData = {
        title: formData.title,
        description: formData.description,
        amount: formData.amount ? parseFloat(formData.amount) : null,
        deadline: formData.deadline,
        eligibility: formData.eligibility || '',
        requirements: formData.requirements || '',
        application_url: formData.application_url || formData.scholarship_link || '',
        contact_email: formData.contact_email || '',
        organization: formData.organization || '',
        field_of_study: formData.field_of_study || '',
        level_of_study: formData.level_of_study || formData.level || '',
        country: formData.country || '',
        coverage: formData.coverage || '',
        financial_benefits_summary: formData.financial_benefits_summary || '',
        eligibility_summary: formData.eligibility_summary || '',
        scholarship_link: formData.scholarship_link || '',
        youtube_link: formData.youtube_link || '',
        thumbnail: formData.thumbnail || '',
        is_active: formData.isOpen !== false
      };

      const response = await adminApiService.updateScholarship(selectedScholarship.id, submitData);

      if (!response.success) {
        throw new Error(response.message || 'Failed to update scholarship');
      }

      await fetchScholarships();
      setShowEditModal(false);
      setSelectedScholarship(null);
      alert('Scholarship updated successfully!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to update scholarship');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!window.confirm('Are you sure you want to delete this scholarship?')) {
      return;
    }

    try {
      const response = await adminApiService.deleteScholarship(id);

      if (!response.success) {
        throw new Error(response.message || 'Failed to delete scholarship');
      }

      setScholarships(scholarships.filter(s => s.id !== id));
      alert('Scholarship deleted successfully!');
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to delete scholarship');
    }
  };

  const handleEdit = (scholarship: Scholarship) => {
    setSelectedScholarship(scholarship);
    setShowEditModal(true);
  };

  const handleBulkImport = async () => {
    try {
      setIsSubmitting(true);

      // Parse the bulk import data (expecting JSON format)
      let scholarshipsData;
      try {
        scholarshipsData = JSON.parse(bulkImportData);
      } catch (parseError) {
        alert('Invalid JSON format. Please check your data.');
        return;
      }

      // Ensure it's an array
      if (!Array.isArray(scholarshipsData)) {
        alert('Data must be an array of scholarships.');
        return;
      }

      // Validate required fields for each scholarship
      const requiredFields = ['title', 'description', 'deadline'];
      for (let i = 0; i < scholarshipsData.length; i++) {
        const scholarship = scholarshipsData[i];
        for (const field of requiredFields) {
          if (!scholarship[field]) {
            alert(`Missing required field "${field}" in scholarship ${i + 1}`);
            return;
          }
        }
      }

      // Send bulk import request using service
      const response = await adminApiService.bulkImportScholarships(scholarshipsData);

      if (response.success) {
        alert(`Bulk import completed! ${response.data?.imported || scholarshipsData.length} scholarships imported successfully.`);
        fetchScholarships(); // Refresh the list
        setShowBulkImportModal(false);
        setBulkImportData('');
      } else {
        alert('Bulk import failed: ' + (response.message || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error in bulk import:', error);
      alert('Failed to process bulk import');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleView = (scholarship: Scholarship) => {
    // Open scholarship detail in new tab
    window.open(`/scholarships/${scholarship.id}`, '_blank');
  };

  const handleCloseModals = () => {
    setShowAddModal(false);
    setShowEditModal(false);
    setSelectedScholarship(null);
  };

  const filteredScholarships = scholarships.filter(scholarship => {
    const matchesSearch = scholarship.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scholarship.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = !filterLevel || scholarship.level === filterLevel;
    const matchesCountry = !filterCountry || scholarship.country === filterCountry;
    
    return matchesSearch && matchesLevel && matchesCountry;
  });

  const uniqueLevels = [...new Set(scholarships.map(s => s.level))].filter(Boolean);
  const uniqueCountries = [...new Set(scholarships.map(s => s.country))].filter(Boolean);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Scholarship Management</h1>
          <p className="text-gray-600">Manage scholarships, add new ones, and update existing entries</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <PlusOutlined />
            <span>Add Scholarship</span>
          </button>
          <button
            onClick={() => setShowBulkImportModal(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <UploadOutlined />
            <span>Bulk Import</span>
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <SearchOutlined style={{ position: 'absolute', left: '12px', top: '12px', color: '#9CA3AF' }} />
            <input
              type="text"
              placeholder="Search scholarships..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <select
            value={filterLevel}
            onChange={(e) => setFilterLevel(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Levels</option>
            {uniqueLevels.map(level => (
              <option key={level} value={level}>{level}</option>
            ))}
          </select>
          <select
            value={filterCountry}
            onChange={(e) => setFilterCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Countries</option>
            {uniqueCountries.map(country => (
              <option key={country} value={country}>{country}</option>
            ))}
          </select>
          <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center space-x-2">
            <FilterOutlined />
            <span>More Filters</span>
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-blue-600">{scholarships.length}</div>
          <div className="text-sm text-gray-600">Total Scholarships</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-green-600">
            {scholarships.filter(s => s.isOpen).length}
          </div>
          <div className="text-sm text-gray-600">Open Applications</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-orange-600">
            {scholarships.filter(s => !s.isOpen).length}
          </div>
          <div className="text-sm text-gray-600">Closed Applications</div>
        </div>
        <div className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="text-2xl font-bold text-purple-600">{uniqueCountries.length}</div>
          <div className="text-sm text-gray-600">Countries</div>
        </div>
      </div>

      {/* Scholarships Table */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Scholarship
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Country
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Deadline
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredScholarships.map((scholarship) => (
                <tr key={scholarship.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {scholarship.title}
                      </div>
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {scholarship.description}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {scholarship.country}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {scholarship.level}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(scholarship.deadline).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      scholarship.isOpen
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {scholarship.isOpen ? 'Open' : 'Closed'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleView(scholarship)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View Scholarship"
                      >
                        <EyeOutlined />
                      </button>
                      <button
                        onClick={() => handleEdit(scholarship)}
                        className="text-green-600 hover:text-green-900"
                        title="Edit Scholarship"
                      >
                        <EditOutlined />
                      </button>
                      <button
                        onClick={() => handleDelete(scholarship.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete Scholarship"
                      >
                        <DeleteOutlined />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredScholarships.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No scholarships found matching your criteria.</div>
          </div>
        )}
      </div>

      {/* Add Scholarship Modal */}
      {showAddModal && (
        <Modal
          open={showAddModal}
          onCancel={handleCloseModals}
          title="Add New Scholarship"
          footer={null}
        >
          <ScholarshipForm
            scholarship={null}
            onSubmit={handleCreateScholarship}
            onCancel={handleCloseModals}
          />
          {isSubmitting && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Creating scholarship...</p>
              </div>
            </div>
          )}
        </Modal>
      )}

      {/* Edit Scholarship Modal */}
      {showEditModal && selectedScholarship && (
        <Modal
          open={showEditModal}
          onCancel={handleCloseModals}
          title="Edit Scholarship"
          footer={null}
        >
          <ScholarshipForm
            scholarship={selectedScholarship}
            onSubmit={handleUpdateScholarship}
            onCancel={handleCloseModals}
          />
          {isSubmitting && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Updating scholarship...</p>
              </div>
            </div>
          )}
        </Modal>
      )}

      {/* Bulk Import Modal */}
      {showBulkImportModal && (
        <Modal onClose={() => setShowBulkImportModal(false)}>
          <div className="p-6">
            <h2 className="text-xl font-bold mb-4">Bulk Import Scholarships</h2>
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                Paste your scholarship data in JSON format. Each scholarship should have at least: title, description, and deadline.
              </p>
              <p className="text-xs text-gray-500 mb-4">
                Example format: {`[{"title": "Scholarship Name", "description": "Description here", "deadline": "2024-12-31", "amount": 5000, "eligibility": "Requirements"}]`}
              </p>
              <textarea
                value={bulkImportData}
                onChange={(e) => setBulkImportData(e.target.value)}
                placeholder="Paste JSON data here..."
                className="w-full h-64 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent font-mono text-sm"
              />
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowBulkImportModal(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                onClick={handleBulkImport}
                disabled={isSubmitting || !bulkImportData.trim()}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Importing...' : 'Import Scholarships'}
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ScholarshipManager;
