import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import authService from '../services/authService';

// Types
interface Admin {
  id: number;
  name: string;
  email: string;
  role: string;
  isMainAdmin: boolean;
  privileges: string[];
  twoFactorEnabled: boolean;
}

interface AuthContextType {
  // State
  admin: Admin | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshAuth: () => Promise<void>;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Clear error
  const clearError = () => setError(null);

  // Check authentication status
  const checkAuthStatus = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log('🔍 AuthContext: Checking authentication status...');

      const response = await authService.getAdminProfile();
      console.log('✅ AuthContext: Profile response received:', response.success);

      if (response.success && response.data?.admin) {
        setAdmin(response.data.admin);
        setIsAuthenticated(true);
        console.log('✅ AuthContext: User authenticated successfully');
      } else {
        setAdmin(null);
        setIsAuthenticated(false);
        console.log('⚠️ AuthContext: Invalid profile response structure');
      }
    } catch (error: any) {
      // Handle authentication errors gracefully
      console.log('🔍 AuthContext: Auth check failed (normal for unauthenticated users):', error.message);
      setAdmin(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Check authentication status on mount (only if not on login page)
  useEffect(() => {
    // Don't auto-check auth on login page to prevent auto-login behavior
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/admin/login') && !currentPath.includes('/admin/login-test')) {
      console.log('🔍 AuthContext: Checking authentication status on mount...');
      checkAuthStatus();
    } else {
      console.log('🔍 AuthContext: Skipping auth check on login page');
      // CRITICAL FIX: Set loading to false when skipping auth check
      setIsLoading(false);
    }
  }, [checkAuthStatus]);

  // Login function with enhanced error handling
  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('🔐 AuthContext: Starting login process...');

      const response = await authService.adminLogin(email, password);
      console.log('✅ AuthContext: Login response received:', response.success);

      if (response.success && response.data?.admin) {
        setAdmin(response.data.admin);
        setIsAuthenticated(true);
        console.log('✅ AuthContext: Authentication state updated successfully');

        // Verify authentication immediately after login
        setTimeout(async () => {
          try {
            await checkAuthStatus();
            console.log('✅ AuthContext: Post-login auth verification successful');
          } catch (verifyError) {
            console.warn('⚠️ AuthContext: Post-login verification failed:', verifyError);
          }
        }, 100);

      } else {
        throw new Error(response.message || 'Login failed - invalid response');
      }
    } catch (error: any) {
      console.error('❌ AuthContext: Login failed:', error.message);
      setError(error.message || 'Login failed - please try again');
      setAdmin(null);
      setIsAuthenticated(false);
      throw error; // Re-throw for component handling
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      setIsLoading(true);
      // Clear local state first to prevent UI flickering
      setAdmin(null);
      setIsAuthenticated(false);

      // Then attempt server-side logout
      await authService.adminLogout();

      // Clear any persistent browser data
      localStorage.clear();
      sessionStorage.clear();

      // Force reload to clear any cached data
      window.location.href = '/admin/login';
    } catch (error: any) {
      console.error('Logout error:', error);
      // Continue with logout even if API call fails
      // Local state is already cleared above
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = '/admin/login';
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh authentication status
  const refreshAuth = async () => {
    await checkAuthStatus();
  };

  const value: AuthContextType = {
    admin,
    isAuthenticated,
    isLoading,
    error,
    login,
    logout,
    clearError,
    refreshAuth
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
