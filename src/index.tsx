import './styles/opportunity-cards.css';

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import validateEnv from './utils/envValidator';

// Fix for mobile Safari viewport height issues
function setViewportHeight() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// Set initial viewport height
setViewportHeight();

// Update on resize and orientation change
window.addEventListener('resize', setViewportHeight);
window.addEventListener('orientationchange', () => {
  setTimeout(setViewportHeight, 100);
});

// Validate environment variables
validateEnv();

// Log application startup information
console.info(`Starting MaBourse application in ${process.env.NODE_ENV} mode`);
console.info(`API URL: ${process.env.REACT_APP_API_URL}`);
console.info(`Using real API: ${process.env.REACT_APP_USE_REAL_API}`);

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);