import React, { useState, useEffect, useCallback } from 'react';
import { Spin, Alert } from 'antd';
import EnhancedOpportunityCard from '../components/EnhancedOpportunityCard';
import GreatYOPPagination from '../components/GreatYOPPagination';
import PageEndSuggestions from '../components/PageEndSuggestions';
import { getOpportunityImage } from '../utils/opportunityImageUtils';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
  slug?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const Opportunities: React.FC = () => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 12,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  const fetchOpportunities = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters like scholarships page
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());
      params.append('active', 'true');
      params.append('orderBy', 'deadline');
      params.append('orderDirection', 'ASC');

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/opportunities/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch opportunities');
      }

      const data = await response.json();
      console.log('Opportunities API response:', data);

      // Handle the correct API response format like scholarships: { success: true, data: [...], pagination: {...} }
      const opportunitiesData = data.data || data.opportunities || [];
      const paginationData = data.pagination || {};

      setOpportunities(opportunitiesData);
      setPagination(paginationData || {
        total: opportunitiesData.length || 0,
        page: 1,
        limit: 12,
        totalPages: Math.ceil((opportunitiesData.length || 0) / 12),
        hasNextPage: false,
        hasPreviousPage: false
      });
      setError(null);
    } catch (error) {
      console.error('Error fetching opportunities:', error);

      // Provide fallback data with local images like scholarships do
      const fallbackOpportunities = [
        {
          id: 1,
          title: 'Stage en Développement Web - Entreprise Tech',
          description: 'Opportunité de stage de 6 mois dans une entreprise technologique innovante. Développement d\'applications web modernes avec React et Node.js.',
          type: 'internship' as const,
          organization: 'TechCorp France',
          location: 'Paris, France',
          isRemote: false,
          deadline: '2024-12-31',
          isActive: true,
          thumbnail: '/uploads/opportunities/internship-1.jpg'
        },
        {
          id: 2,
          title: 'Formation Certifiante en Intelligence Artificielle',
          description: 'Formation intensive de 3 mois en IA et Machine Learning. Certification reconnue par l\'industrie.',
          type: 'training' as const,
          organization: 'AI Institute',
          location: 'Lyon, France',
          isRemote: true,
          deadline: '2024-11-30',
          isActive: true,
          thumbnail: '/uploads/opportunities/training-1.jpg'
        },
        {
          id: 3,
          title: 'Conférence Internationale sur la Cybersécurité',
          description: 'Conférence de 2 jours réunissant les experts mondiaux en cybersécurité. Networking et sessions techniques.',
          type: 'conference' as const,
          organization: 'CyberSec Global',
          location: 'Marseille, France',
          isRemote: false,
          deadline: '2024-10-15',
          isActive: true,
          thumbnail: '/uploads/opportunities/conference-1.jpg'
        },
        {
          id: 4,
          title: 'Atelier Pratique - Design UX/UI',
          description: 'Atelier hands-on de 2 jours pour apprendre les meilleures pratiques du design UX/UI.',
          type: 'workshop' as const,
          organization: 'Design Academy',
          location: 'Toulouse, France',
          isRemote: false,
          deadline: '2024-09-30',
          isActive: true,
          thumbnail: '/uploads/opportunities/workshop-1.jpg'
        },
        {
          id: 5,
          title: 'Concours Innovation Startup 2024',
          description: 'Concours national pour les startups innovantes. Prix de 50 000€ et accompagnement personnalisé.',
          type: 'competition' as const,
          organization: 'French Tech',
          location: 'Paris, France',
          isRemote: false,
          deadline: '2024-08-31',
          isActive: true,
          thumbnail: '/uploads/opportunities/competition-1.jpg'
        },
        {
          id: 6,
          title: 'Formation en Gestion de Projet Agile',
          description: 'Formation certifiante Scrum Master et Product Owner. Méthodes agiles pour la gestion de projet.',
          type: 'training' as const,
          organization: 'Agile Institute',
          location: 'Bordeaux, France',
          isRemote: true,
          deadline: '2024-12-15',
          isActive: true,
          thumbnail: '/uploads/opportunities/training-2.jpg'
        }
      ];

      setOpportunities(fallbackOpportunities);
      setPagination({
        total: fallbackOpportunities.length,
        page: 1,
        limit: 12,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false
      });
      setError(null); // Don't show error when using fallback data
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit]);

  useEffect(() => {
    fetchOpportunities();
  }, [fetchOpportunities]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleOpportunityClick = (id: number, slug?: string) => {
    // Navigate to opportunity details page using slug if available (same pattern as scholarships)
    if (slug) {
      window.location.href = `/opportunite/${slug}`;
    } else {
      window.location.href = `/opportunities/${id}`;
    }
  };

  // Helper function to convert opportunity type to display name (like scholarship levels)
  const getTypeDisplayName = (type: string): string => {
    const typeMap: Record<string, string> = {
      'internship': 'Stage',
      'training': 'Formation',
      'conference': 'Conférence',
      'workshop': 'Atelier',
      'competition': 'Concours'
    };
    return typeMap[type] || type;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20">
      {/* Standard Hero Section - Matching Country Page Style */}
      <section className="bg-white pt-20 pb-4" style={{ paddingTop: '4rem' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center mb-3">
            <span className="text-3xl mr-3">💼</span>
            <h1 style={{
              fontSize: '24px',
              marginBottom: '0',
              color: '#2563eb',
              fontWeight: '700',
              textTransform: 'capitalize'
            }}>
              Opportunités Professionnelles
            </h1>
          </div>

          {/* Hero Description */}
          <div className="max-w-4xl">
            <p style={{
              fontSize: '16px',
              lineHeight: '1.6',
              color: '#6b7280',
              marginBottom: '1rem',
              fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif'
            }}>
              Découvrez les meilleures opportunités professionnelles pour développer votre carrière : stages, formations,
              conférences, ateliers et concours. Explorez notre sélection d'opportunités soigneusement choisies pour vous
              aider à atteindre vos objectifs professionnels et à construire un avenir prometteur dans votre domaine d'expertise.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
              <div className="flex items-center text-sm text-gray-600">
                <svg className="h-4 w-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Stages professionnels
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <svg className="h-4 w-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Formations spécialisées
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <svg className="h-4 w-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Conférences internationales
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <svg className="h-4 w-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Ateliers pratiques
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <svg className="h-4 w-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Concours professionnels
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <svg className="h-4 w-4 text-primary mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Opportunités à distance
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="w-full">{/* Full width layout without sidebar */}

          {/* Opportunities Grid */}
          <div id="opportunities-section" className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold text-gray-900">
                Toutes les Opportunités
              </h2>
            </div>
            {loading ? (
              <div className="flex justify-center items-center py-16">
                <Spin size="large" tip="Chargement des opportunités..." />
              </div>
            ) : error ? (
              <Alert
                message="Erreur"
                description={error}
                type="error"
                showIcon
                className="mb-6 rounded-xl shadow-md"
              />
            ) : (
              <>
                <div className="gy-pcard-wrap country-page op-cards">
                  {opportunities.map((opportunity, index) => (
                    <EnhancedOpportunityCard
                      key={opportunity.id}
                      id={opportunity.id}
                      title={opportunity.title}
                      thumbnail={getOpportunityImage(opportunity.type, opportunity.thumbnail, index)}
                      deadline={opportunity.deadline}
                      isActive={opportunity.isActive}
                      onClick={(id, slug) => handleOpportunityClick(id, slug)}
                      type={opportunity.type}
                      organization={opportunity.organization}
                      location={opportunity.location}
                      isRemote={opportunity.isRemote}
                      featured={false}
                      index={index}
                      variant="greatyop"
                    />
                  ))}
                </div>

                {/* No Results Message */}
                {opportunities.length === 0 && (
                  <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune opportunité trouvée</h3>
                    <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                      Aucune opportunité n'est disponible pour le moment. Revenez bientôt pour découvrir de nouvelles possibilités.
                    </p>
                  </div>
                )}

                {/* GreatYOP Pagination */}
                {pagination.total > 0 && (
                  <div className="mt-8">
                    <GreatYOPPagination
                      current={pagination.page}
                      total={pagination.total}
                      pageSize={pagination.limit}
                      onChange={handlePageChange}
                      showQuickJumper={false}
                    />
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Page End Suggestions */}
        <PageEndSuggestions
          currentPageType="opportunity"
        />
      </div>
    </div>
  );
};

export default Opportunities;
