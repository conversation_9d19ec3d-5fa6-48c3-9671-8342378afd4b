import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import { calculateDaysRemaining } from '../utils/dateFormatter';
import UnifiedSidebar from '../components/UnifiedSidebar';
import CommentsSection from '../components/CommentsSection';
import ProfessionalNewsletterSection from '../components/ProfessionalNewsletterSection';
import AdPlacement from '../components/AdPlacement';
import { Spin, Alert, Button, Tag, Badge } from 'antd';
import {
  CalendarOutlined,
  EnvironmentOutlined,
  BankOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  LinkOutlined,
  ClockCircleOutlined,
  UserOutlined,
  GlobalOutlined,
  TrophyOutlined,
  BookOutlined
} from '@ant-design/icons';

interface Opportunity {
  id: number;
  title: string;
  description: string;
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  organization: string;
  location: string;
  isRemote: boolean;
  deadline: string;
  startDate?: string;
  endDate?: string;
  applicationLink?: string;
  requirements?: string;
  benefits?: string;
  eligibilityCriteria?: string;
  requiredDocuments?: string;
  howToApply?: string;
  thumbnail?: string;
  isActive: boolean;
  tags?: string[];
  contactEmail?: string;
  website?: string;
  slug?: string;
  featured?: boolean;
  viewCount?: number;
  status?: string;
  metaTitle?: string;
  metaDescription?: string;
  priority?: number;
  createdAt?: string;
  updatedAt?: string;
}

const OpportunityDetailPage: React.FC = () => {
  const { id, slug } = useParams<{ id?: string; slug?: string }>();
  const navigate = useNavigate();
  const { language } = useLanguage();
  const [opportunity, setOpportunity] = useState<Opportunity | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const extractIdFromSlug = (slug: string): number | null => {
    const parts = slug.split('-');
    const lastPart = parts[parts.length - 1];
    const id = parseInt(lastPart, 10);
    return isNaN(id) ? null : id;
  };

  const getTypeLabel = (type: string): string => {
    const labels = {
      internship: 'Stage',
      training: 'Formation',
      conference: 'Conférence',
      workshop: 'Atelier',
      competition: 'Concours'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getTypeIcon = (type: string): string => {
    const icons = {
      internship: '🎓',
      training: '📚',
      conference: '🎤',
      workshop: '🔧',
      competition: '🏆'
    };
    return icons[type as keyof typeof icons] || '📋';
  };

  useEffect(() => {
    const fetchOpportunity = async () => {
      setLoading(true);
      setError(null);

      try {
        let opportunityId: string | null = null;

        if (id) {
          opportunityId = id;
        } else if (slug) {
          const extractedId = extractIdFromSlug(slug);
          if (extractedId) {
            opportunityId = extractedId.toString();
          } else {
            throw new Error('Invalid slug format');
          }
        }

        if (opportunityId) {
          const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
          const response = await fetch(`${apiUrl}/api/opportunities/${opportunityId}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          const opportunityData = data.data || data;

          if (opportunityData) {
            setOpportunity(opportunityData);
          } else {
            throw new Error('Invalid API response format');
          }
        } else {
          throw new Error('No valid ID or slug provided');
        }
      } catch (err: any) {
        console.error('Error fetching opportunity:', err);
        setError(`Échec du chargement des détails de l'opportunité: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchOpportunity();
  }, [id, slug]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Spin size="large" tip="Chargement de l'opportunité..." />
      </div>
    );
  }

  if (error || !opportunity) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Alert
          message="Erreur"
          description={error || "Opportunité non trouvée"}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={() => navigate('/opportunities')}>
              Retour aux opportunités
            </Button>
          }
        />
      </div>
    );
  }

  const { formattedText, isOpen: isNotExpired } = calculateDaysRemaining(opportunity.deadline, language);
  const opportunityStatus = isNotExpired !== undefined ? isNotExpired : opportunity.isActive;

  const sidebarConfig = {
    type: 'opportunities' as const,
    currentItem: opportunity.type,
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>{opportunity.metaTitle || `${opportunity.title} - ${opportunity.organization} | MaBourse`}</title>
        <meta name="description" content={opportunity.metaDescription || opportunity.description.substring(0, 160)} />
        <meta name="keywords" content={opportunity.tags?.join(', ') || `${opportunity.type}, ${opportunity.organization}`} />
      </Helmet>

      {/* Compact Professional Hero Section */}
      <section className="bg-white pt-6 pb-2 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-xl sm:text-2xl font-semibold text-gray-900 leading-tight">
            {opportunity.title}
          </h2>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-5">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-5">
            {/* Key Information Section */}
            <section className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <FileTextOutlined className="text-primary mr-2" />
                Informations clés
              </h2>
              <ul className="text-sm space-y-1.5">
                <li className="flex items-start">
                  <CalendarOutlined className="text-primary mr-2 mt-0.5" />
                  <div>
                    <div className="flex items-center">
                      <span className="font-medium text-gray-700">Date limite :</span>
                      <span className="text-gray-600 ml-1">
                        {new Date(opportunity.deadline).toLocaleDateString()}
                      </span>

                      {/* Status badge */}
                      {!opportunityStatus ? (
                        <Badge
                          className="ml-2"
                          count="Expiré"
                          style={{ backgroundColor: '#ff4d4f' }}
                        />
                      ) : (
                        <Badge
                          className="ml-2"
                          count="Actif"
                          style={{ backgroundColor: '#52c41a' }}
                        />
                      )}
                    </div>

                    {/* Days remaining indicator */}
                    {opportunityStatus && (
                      <div className="text-xs text-gray-500 mt-1 flex items-center">
                        <ClockCircleOutlined className="mr-1" />
                        {formattedText}
                      </div>
                    )}
                  </div>
                </li>

                <li className="flex items-center">
                  <BankOutlined className="text-primary mr-2" />
                  <span className="font-medium text-gray-700">Organisation :</span>
                  <span className="text-gray-600 ml-1">{opportunity.organization}</span>
                </li>

                <li className="flex items-center">
                  <EnvironmentOutlined className="text-primary mr-2" />
                  <span className="font-medium text-gray-700">Lieu :</span>
                  <span className="text-gray-600 ml-1">
                    {opportunity.isRemote ? 'À distance' : opportunity.location}
                  </span>
                </li>

                <li className="flex items-center">
                  <UserOutlined className="text-primary mr-2" />
                  <span className="font-medium text-gray-700">Type :</span>
                  <span className="text-gray-600 ml-1">{getTypeLabel(opportunity.type)}</span>
                </li>

                {opportunity.startDate && (
                  <li className="flex items-center">
                    <CalendarOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Date de début :</span>
                    <span className="text-gray-600 ml-1">
                      {new Date(opportunity.startDate).toLocaleDateString()}
                    </span>
                  </li>
                )}

                {opportunity.endDate && (
                  <li className="flex items-center">
                    <CalendarOutlined className="text-primary mr-2" />
                    <span className="font-medium text-gray-700">Date de fin :</span>
                    <span className="text-gray-600 ml-1">
                      {new Date(opportunity.endDate).toLocaleDateString()}
                    </span>
                  </li>
                )}
              </ul>
            </section>

            {/* Description Section */}
            <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
              <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                <FileTextOutlined className="text-primary mr-2" />
                Description
              </h2>
              <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                <p className="whitespace-pre-line">{opportunity.description}</p>
              </div>
            </section>

            {/* Eligibility Criteria Section */}
            {opportunity.eligibilityCriteria && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <CheckCircleOutlined className="text-primary mr-2" />
                  Critère d'éligibilité
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{opportunity.eligibilityCriteria}</p>
                </div>
              </section>
            )}

            {/* Required Documents Section */}
            {opportunity.requiredDocuments && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <FileTextOutlined className="text-primary mr-2" />
                  Documents demandés
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{opportunity.requiredDocuments}</p>
                </div>
              </section>
            )}

            {/* How to Apply Section - Prominent */}
            {opportunity.howToApply && (
              <section className="bg-gradient-to-r from-primary/5 via-primary/3 to-secondary/5 rounded-xl p-6 shadow-md border-2 border-primary/20">
                <h2 className="text-xl font-bold text-primary mb-4 flex items-center">
                  <TrophyOutlined className="text-primary mr-2" />
                  Comment postuler
                </h2>
                <div className="prose max-w-none text-gray-700 text-sm leading-relaxed">
                  <p className="whitespace-pre-line font-medium">{opportunity.howToApply}</p>
                </div>
              </section>
            )}

            {/* Legacy sections for backward compatibility */}
            {opportunity.requirements && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <CheckCircleOutlined className="text-primary mr-2" />
                  Exigences
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{opportunity.requirements}</p>
                </div>
              </section>
            )}

            {/* Benefits Section */}
            {opportunity.benefits && (
              <section className="bg-white rounded-xl p-5 shadow-sm border border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 mb-3 flex items-center">
                  <TrophyOutlined className="text-primary mr-2" />
                  Avantages
                </h2>
                <div className="prose max-w-none text-gray-600 text-sm leading-relaxed">
                  <p className="whitespace-pre-line">{opportunity.benefits}</p>
                </div>
              </section>
            )}



            {/* Application Section */}
            {opportunityStatus && opportunity.applicationLink && (
              <section className="bg-gradient-to-r from-primary via-primary/90 to-secondary rounded-xl shadow-sm p-5 text-white">
                <h2 className="text-xl font-bold mb-3 flex items-center">
                  <LinkOutlined className="text-white mr-2" />
                  Postuler maintenant
                </h2>
                <p className="mb-4 text-sm text-white/90">Ne manquez pas cette opportunité ! Postulez dès maintenant.</p>
                <a
                  href={opportunity.applicationLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-4 py-2 bg-white text-primary font-medium rounded-lg hover:bg-gray-100 transition-colors duration-200 text-sm"
                >
                  Postuler
                  <LinkOutlined className="ml-2" />
                </a>
              </section>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            {/* Newsletter Subscription */}
            <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
              <h3 className="text-base font-bold text-gray-900 mb-2">Restez informé</h3>
              <p className="text-xs text-gray-600 mb-3">
                Recevez les dernières opportunités directement dans votre boîte mail.
              </p>
              <form className="space-y-2">
                <div>
                  <input
                    type="email"
                    placeholder="Votre adresse email"
                    className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                <Button
                  type="primary"
                  size="small"
                  className="w-full"
                >
                  S'abonner
                </Button>
                <p className="text-xs text-gray-500 mt-1">
                  Pas de spam, désabonnement facile.
                </p>
              </form>
            </div>

            {/* Dynamic Sidebar using UnifiedSidebar */}
            <UnifiedSidebar config={sidebarConfig} />

            {/* Desktop Ad */}
            <div className="hidden lg:block">
              <AdPlacement
                adSlot="9876543210"
                adSize="rectangle"
                responsive={false}
              />
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <CommentsSection pageType="opportunity" pageId={opportunity.id.toString()} />
        </div>

        {/* Professional Newsletter Section - After Comments */}
        <ProfessionalNewsletterSection type="opportunities" />
      </div>
    </>
  );
};

export default OpportunityDetailPage;
