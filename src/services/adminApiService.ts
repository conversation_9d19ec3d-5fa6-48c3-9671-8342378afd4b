/**
 * ADMIN API SERVICE
 * Production-grade service for admin portal API interactions
 * ZERO TOLERANCE FOR INCOMPLETE IMPLEMENTATIONS
 */

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail?: string;
  coverage?: string;
  financialBenefitsSummary?: string;
  eligibilitySummary?: string;
  scholarshipLink?: string;
  youtubeLink?: string;
  createdAt: string;
  updatedAt: string;
}

interface Message {
  id: number;
  name: string;
  email: string;
  subject: string;
  content: string;
  status: 'pending' | 'read' | 'replied' | 'archived';
  createdAt: string;
  updatedAt: string;
}

interface AdminStats {
  totalScholarships: number;
  totalMessages: number;
  totalSubscribers: number;
  totalAdmins: number;
}

class AdminApiService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Authentication
  async login(email: string, password: string): Promise<ApiResponse> {
    return this.makeRequest('/api/auth/admin/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async logout(): Promise<ApiResponse> {
    return this.makeRequest('/api/auth/admin/logout', {
      method: 'POST',
    });
  }

  async getCurrentAdmin(): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/me');
  }

  // Dashboard Stats
  async getStats(): Promise<ApiResponse<AdminStats>> {
    return this.makeRequest<AdminStats>('/api/admin/stats');
  }

  // Scholarships Management
  async getScholarships(page = 1, limit = 10): Promise<ApiResponse<Scholarship[]>> {
    return this.makeRequest<Scholarship[]>(`/api/scholarships?page=${page}&limit=${limit}`);
  }

  async getScholarship(id: number): Promise<ApiResponse<Scholarship>> {
    return this.makeRequest<Scholarship>(`/api/scholarships/${id}`);
  }

  async createScholarship(scholarshipData: Partial<Scholarship>): Promise<ApiResponse<Scholarship>> {
    return this.makeRequest<Scholarship>('/api/admin/scholarships', {
      method: 'POST',
      body: JSON.stringify(scholarshipData),
    });
  }

  async updateScholarship(id: number, scholarshipData: Partial<Scholarship>): Promise<ApiResponse<Scholarship>> {
    return this.makeRequest<Scholarship>(`/api/admin/scholarships/${id}`, {
      method: 'PUT',
      body: JSON.stringify(scholarshipData),
    });
  }

  async deleteScholarship(id: number): Promise<ApiResponse> {
    return this.makeRequest(`/api/admin/scholarships/${id}`, {
      method: 'DELETE',
    });
  }

  // Messages Management
  async getMessages(): Promise<ApiResponse<Message[]>> {
    return this.makeRequest<Message[]>('/api/messages');
  }

  async getMessage(id: number): Promise<ApiResponse<Message>> {
    return this.makeRequest<Message>(`/api/messages/${id}`);
  }

  async updateMessageStatus(id: number, status: string): Promise<ApiResponse<Message>> {
    return this.makeRequest<Message>(`/api/admin/messages/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  async deleteMessage(id: number): Promise<ApiResponse> {
    return this.makeRequest(`/api/admin/messages/${id}`, {
      method: 'DELETE',
    });
  }

  async replyToMessage(id: number, reply: string): Promise<ApiResponse> {
    return this.makeRequest(`/api/admin/messages/${id}/reply`, {
      method: 'POST',
      body: JSON.stringify({ reply }),
    });
  }

  // Opportunities Management
  async getOpportunities(page = 1, limit = 10): Promise<ApiResponse> {
    return this.makeRequest(`/api/opportunities?page=${page}&limit=${limit}`);
  }

  async createOpportunity(opportunityData: any): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/opportunities', {
      method: 'POST',
      body: JSON.stringify(opportunityData),
    });
  }

  async updateOpportunity(id: number, opportunityData: any): Promise<ApiResponse> {
    return this.makeRequest(`/api/admin/opportunities/${id}`, {
      method: 'PUT',
      body: JSON.stringify(opportunityData),
    });
  }

  async deleteOpportunity(id: number): Promise<ApiResponse> {
    return this.makeRequest(`/api/admin/opportunities/${id}`, {
      method: 'DELETE',
    });
  }

  // Newsletter Management
  async getSubscribers(): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/newsletter/subscribers');
  }

  async sendNewsletter(subject: string, content: string): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/newsletter/send', {
      method: 'POST',
      body: JSON.stringify({ subject, content }),
    });
  }

  // File Upload
  async uploadFile(file: File, type: 'scholarship' | 'opportunity' = 'scholarship'): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return this.makeRequest('/api/admin/upload', {
      method: 'POST',
      body: formData,
      headers: {}, // Don't set Content-Type for FormData
    });
  }

  // Bulk Operations
  async bulkDeleteScholarships(ids: number[]): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/scholarships/bulk-delete', {
      method: 'DELETE',
      body: JSON.stringify({ ids }),
    });
  }

  async bulkUpdateScholarships(updates: Array<{ id: number; data: Partial<Scholarship> }>): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/scholarships/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({ updates }),
    });
  }

  // Export/Import
  async exportScholarships(format: 'csv' | 'json' = 'csv'): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/admin/scholarships/export?format=${format}`, {
      credentials: 'include',
    });
    
    if (!response.ok) {
      throw new Error(`Export failed: ${response.status}`);
    }
    
    return response.blob();
  }

  async importScholarships(file: File): Promise<ApiResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.makeRequest('/api/admin/scholarships/import', {
      method: 'POST',
      body: formData,
      headers: {}, // Don't set Content-Type for FormData
    });
  }

  async bulkImportScholarships(scholarships: any[]): Promise<ApiResponse> {
    return this.makeRequest('/api/admin/scholarships/bulk-import', {
      method: 'POST',
      body: JSON.stringify({ scholarships }),
    });
  }
}

// Export singleton instance
const adminApiService = new AdminApiService();
export default adminApiService;

// Export types for use in components
export type { Scholarship, Message, AdminStats, ApiResponse };
