import axios from 'axios';
import { Scholarship } from '../components/ScholarshipGrid';

// Base API URL
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

/**
 * Service for fetching and filtering scholarships
 */
const scholarshipService = {
  /**
   * Get all scholarships with NUCLEAR fallback
   */
  getAllScholarships: async (): Promise<Scholarship[]> => {
    try {
      console.log('🚀 NUCLEAR: Fetching scholarships from API...');

      // NUCLEAR FIX: Multiple API attempts
      let response;
      let success = false;

      // Method 1: Try main endpoint
      try {
        response = await axios.get(`${API_URL}/api/scholarships`, { timeout: 10000 });
        success = true;
        console.log('✅ NUCLEAR: Main API successful:', response.data);
      } catch (mainError) {
        console.log('⚠️ NUCLEAR: Main API failed, trying search endpoint...');

        // Method 2: Try search endpoint
        try {
          response = await axios.get(`${API_URL}/api/scholarships/search?limit=50`, { timeout: 10000 });
          success = true;
          console.log('✅ NUCLEAR: Search API successful:', response.data);
        } catch (searchError) {
          console.log('⚠️ NUCLEAR: Search API failed, trying direct fetch...');

          // Method 3: Direct fetch
          try {
            const directResponse = await fetch(`${API_URL}/api/scholarships`, {
              method: 'GET',
              headers: { 'Content-Type': 'application/json' },
              signal: AbortSignal.timeout(10000)
            });

            if (directResponse.ok) {
              const data = await directResponse.json();
              response = { data };
              success = true;
              console.log('✅ NUCLEAR: Direct fetch successful:', data);
            } else {
              throw new Error(`Direct fetch failed: ${directResponse.status}`);
            }
          } catch (directError) {
            console.log('⚠️ NUCLEAR: All API methods failed, using fallback data');
            return getFallbackScholarships();
          }
        }
      }

      if (success && response) {
        // Handle the correct API response format: { success: true, data: [...] }
        const scholarshipsData = response.data.data || response.data.scholarships || [];

        const mappedScholarships = scholarshipsData.map((scholarship: any) => ({
          id: scholarship.id,
          title: scholarship.title,
          thumbnail: scholarship.thumbnail,
          deadline: scholarship.deadline,
          isOpen: scholarship.isOpen,
          level: scholarship.level,
          fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
          country: scholarship.country || 'France',
        }));

        console.log('✅ NUCLEAR: Scholarships mapped successfully:', mappedScholarships.length, 'items');
        return mappedScholarships;
      } else {
        throw new Error('All API methods failed');
      }
    } catch (error) {
      console.error('🚨 NUCLEAR ERROR in getAllScholarships:', error);
      console.log('🔧 NUCLEAR: Using fallback scholarships');
      return getFallbackScholarships();
    }
  },

  /**
   * Get scholarships by level (Licence, Master, Doctorat)
   */
  getScholarshipsByLevel: async (level: string): Promise<Scholarship[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/scholarships?level=${level}`);
      return response.data.scholarships.map((scholarship: any) => ({
        id: scholarship.id,
        title: scholarship.title,
        thumbnail: scholarship.thumbnail,
        deadline: scholarship.deadline,
        isOpen: scholarship.isOpen,
        level: scholarship.level,
        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
        country: scholarship.country || 'France',
      }));
    } catch (error) {
      console.error(`Error fetching scholarships by level ${level}:`, error);
      // Filter fallback data by level
      return getFallbackScholarships().filter(
        (s) => s.level?.toLowerCase() === level.toLowerCase()
      );
    }
  },

  /**
   * Get scholarships by funding source
   */
  getScholarshipsBySource: async (source: string): Promise<Scholarship[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/scholarships?fundingSource=${source}`);
      return response.data.scholarships.map((scholarship: any) => ({
        id: scholarship.id,
        title: scholarship.title,
        thumbnail: scholarship.thumbnail,
        deadline: scholarship.deadline,
        isOpen: scholarship.isOpen,
        level: scholarship.level,
        fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
        country: scholarship.country || 'France',
      }));
    } catch (error) {
      console.error(`Error fetching scholarships by source ${source}:`, error);
      // Filter fallback data by source
      return getFallbackScholarships().filter(
        (s) => s.fundingSource?.toLowerCase().includes(source.toLowerCase())
      );
    }
  },

  /**
   * Get latest scholarships with NUCLEAR fallback
   */
  getLatestScholarships: async (limit: number = 6): Promise<Scholarship[]> => {
    try {
      console.log('🚀 NUCLEAR: Fetching latest scholarships from API...');

      // NUCLEAR FIX: Multiple API attempts for latest scholarships
      let response;
      let success = false;

      // Method 1: Try sorted endpoint
      try {
        response = await axios.get(`${API_URL}/api/scholarships?sortBy=createdAt&sortOrder=desc&limit=${limit}`, { timeout: 10000 });
        success = true;
        console.log('✅ NUCLEAR: Sorted API successful:', response.data);
      } catch (sortedError) {
        console.log('⚠️ NUCLEAR: Sorted API failed, trying search endpoint...');

        // Method 2: Try search endpoint with limit
        try {
          response = await axios.get(`${API_URL}/api/scholarships/search?limit=${limit}&page=1`, { timeout: 10000 });
          success = true;
          console.log('✅ NUCLEAR: Search API successful:', response.data);
        } catch (searchError) {
          console.log('⚠️ NUCLEAR: Search API failed, trying basic endpoint...');

          // Method 3: Try basic endpoint and slice
          try {
            response = await axios.get(`${API_URL}/api/scholarships`, { timeout: 10000 });
            success = true;
            console.log('✅ NUCLEAR: Basic API successful, will slice to limit');
          } catch (basicError) {
            console.log('⚠️ NUCLEAR: All API methods failed for latest scholarships');
            return getFallbackScholarships().slice(0, limit);
          }
        }
      }

      if (success && response) {
        // Handle the correct API response format: { success: true, data: [...] }
        const scholarshipsData = response.data.data || response.data.scholarships || [];

        const mappedScholarships = scholarshipsData.map((scholarship: any) => ({
          id: scholarship.id,
          title: scholarship.title,
          thumbnail: scholarship.thumbnail,
          deadline: scholarship.deadline,
          isOpen: scholarship.isOpen,
          level: scholarship.level,
          fundingSource: scholarship.fundingSource || determineSourceFromData(scholarship),
          country: scholarship.country || 'France',
        }));

        // Sort by creation date (most recent first) and limit
        const sortedScholarships = mappedScholarships
          .sort((a, b) => new Date(b.deadline).getTime() - new Date(a.deadline).getTime())
          .slice(0, limit);

        console.log('✅ NUCLEAR: Latest scholarships processed successfully:', sortedScholarships.length, 'items');
        return sortedScholarships;
      } else {
        throw new Error('All API methods failed');
      }
    } catch (error) {
      console.error('🚨 NUCLEAR ERROR in getLatestScholarships:', error);
      console.log('🔧 NUCLEAR: Using fallback latest scholarships');
      // Return first 'limit' scholarships from fallback data
      return getFallbackScholarships().slice(0, limit);
    }
  },
};

/**
 * Helper function to determine funding source from scholarship data
 */
function determineSourceFromData(scholarship: any): string {
  if (scholarship.title?.toLowerCase().includes('gouvernement') ||
      scholarship.description?.toLowerCase().includes('gouvernement')) {
    return 'Gouvernement';
  } else if (scholarship.title?.toLowerCase().includes('université') ||
             scholarship.description?.toLowerCase().includes('université')) {
    return 'Université';
  } else {
    return 'Organisation';
  }
}

/**
 * Fallback scholarships data when API is unavailable
 */
function getFallbackScholarships(): Scholarship[] {
  return [
    {
      id: 1,
      title: 'Bourse d\'Excellence en Informatique',
      thumbnail: '/assets/scholarship1.jpg',
      deadline: '2024-06-30',
      isOpen: true,
      level: 'Licence',
      fundingSource: 'Université',
      country: 'France',
    },
    {
      id: 2,
      title: 'Programme de Bourse en Génie Civil',
      thumbnail: '/assets/scholarship2.jpg',
      deadline: '2024-05-15',
      isOpen: true,
      level: 'Master',
      fundingSource: 'Gouvernement',
      country: 'Canada',
    },
    {
      id: 3,
      title: 'Bourse Internationale en Médecine',
      thumbnail: '/assets/scholarship3.jpg',
      deadline: '2024-04-30',
      isOpen: false,
      level: 'Doctorat',
      fundingSource: 'Organisation',
      country: 'Belgique',
    },
    // Add more fallback scholarships as needed
  ];
}

export default scholarshipService;
