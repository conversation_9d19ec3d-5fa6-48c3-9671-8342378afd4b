/* GreatYOP Card Styles - Based on the HTML file structure */

.gy-pcard-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.gy-post-card {
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 12px; /* Slightly more rounded for modern look */
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04); /* Softer shadow */
  cursor: pointer;
  flex: 1;
  min-width: 300px;
  max-width: 400px;
  /* GreatYOP-style rectangular aspect ratio - more width than height */
  aspect-ratio: 4 / 3;
  display: flex;
  flex-direction: column;
}

.gy-post-card:hover {
  transform: translateY(-2px); /* Subtle lift effect */
  box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.15), 0 4px 10px 0 rgba(0, 0, 0, 0.08); /* Enhanced shadow */
  border: 1px solid #d1d5db; /* Slightly darker border on hover */
  transition: all 0.3s ease;
}

.gyp-article-thumb {
  position: relative;
  overflow: hidden;
  height: 55%; /* Increased height for better image display */
  background-color: #f3f4f6; /* Light gray background like GreatYOP */
  flex-shrink: 0;
}

.gyp-article-thumb a {
  display: block;
  width: 100%;
  height: 100%;
}

.gyp-article-thumb img {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Changed from fill to cover for better image proportions */
  object-position: center;
  transition: none; /* Remove image transition */
  display: block;
}

/* Remove image zoom effect - GreatYOP style */
.gy-post-card:hover .gyp-article-thumb img {
  transform: none; /* No image zoom on hover */
}

.gyp-archive-post-header-wrapper {
  padding: 0.75rem 1rem 0.5rem 1rem; /* Reduced padding for better balance */
  flex: 1; /* Allow content area to grow and fill remaining space */
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Better distribution of content */
}

.entry-header {
  margin: 0;
}

.entry-title {
  margin: 0 0 4px; /* Reduced margin */
  font-size: 16px; /* Reduced from 20px for better proportion */
  font-weight: 600;
  line-height: 1.2; /* Tighter line height */
  color: #353535;
  text-transform: capitalize;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
}

.entry-title a,
.entry-title button {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  /* Limit title to 2 lines */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.entry-title a:hover,
.entry-title button:hover {
  color: #3c6c90;
}

/* Entry meta styling - GreatYOP style */
.entry-meta {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem; /* Reduced gap for tighter layout */
}

.country-meta,
.level-meta {
  font-size: 13px; /* Slightly smaller for better proportion */
  color: #555555; /* Darker for better readability */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.4;
  display: flex;
  align-items: center;
  font-weight: 500; /* Slightly bolder */
}

.country-meta i,
.level-meta i {
  margin-right: 0.4rem; /* Reduced margin */
  color: #777777; /* Slightly darker icons */
  font-size: 12px; /* Smaller icons */
}

.gyp-article-thumb button {
  outline: none;
}

.gyp-article-thumb button:focus {
  outline: 2px solid #3c6c90;
  outline-offset: 2px;
}

.gy-pcard-bottom {
  padding: 0.75rem 1rem; /* Reduced horizontal padding */
  border-top: 1px solid #f3f4f6;
  margin-top: auto; /* Push to bottom */
  background: linear-gradient(to bottom, #fafafa, #f8f9fa); /* Subtle gradient */
  flex-shrink: 0; /* Prevent shrinking */
  min-height: 50px; /* Ensure consistent height */
}

.gy-pcard-bottom p {
  margin: 0;
  font-size: 13px; /* Optimized size for deadline text */
  color: #666666; /* Balanced color for readability */
  display: flex;
  align-items: center;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.4;
  font-weight: 500; /* Slightly bolder */
}

.gy-pcard-bottom p i {
  margin-right: 0.4rem;
  color: #888888; /* Subtle icon color */
  font-size: 12px;
}

/* Bottom section layout - using inline styles for better control */
.gy-pcard-bottom > div {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
}

.gy-pcard-bottom > div > div {
  display: flex !important;
  align-items: center !important;
  flex-shrink: 0 !important;
}

.gy-pcard-bottom i {
  color: #9ca3af;
  margin-right: 0.5rem;
}

/* Enhanced bottom section styling */
.gy-pcard-bottom svg {
  flex-shrink: 0;
  color: #9ca3af;
}

.gy-pcard-bottom span {
  font-weight: 500;
  line-height: 1.5;
  color: #767676;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", Arial, sans-serif;
  font-size: 13px;
}

/* Ensure proper text truncation */
.gy-pcard-bottom .truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* FontAwesome icon styles */
.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-hourglass-half:before {
  content: "\f252";
}

.fa-map-marker-alt:before {
  content: "\f3c5";
}

/* Mobile first responsive design */
@media (max-width: 639px) {
  .gy-pcard-wrap {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .gy-post-card {
    min-width: 100%;
    max-width: 100%;
    aspect-ratio: 5 / 4; /* Slightly taller on mobile for better content display */
  }

  .gyp-article-thumb {
    height: 50%; /* Balanced height on mobile */
  }

  .entry-title {
    font-size: 15px; /* Slightly smaller on mobile */
  }
}

/* Tablet layout */
@media (min-width: 640px) {
  .gy-pcard-wrap {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .gy-post-card {
    flex: none;
    max-width: none;
    width: 100%;
    aspect-ratio: 4 / 3; /* GreatYOP-style rectangular aspect ratio */
  }

  .gyp-article-thumb {
    height: 55%; /* Increased height for better image display */
  }

  .entry-title {
    font-size: 16px; /* Standard size on tablet */
  }
}

/* Desktop layout */
@media (min-width: 1024px) {
  .gy-pcard-wrap {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .gy-post-card {
    max-width: none;
    width: 100%;
    aspect-ratio: 4 / 3; /* GreatYOP-style rectangular aspect ratio */
  }

  .gyp-article-thumb {
    height: 55%; /* Increased height for better image display */
  }

  .entry-title {
    font-size: 16px; /* Standard size on desktop */
  }
}

/* Large desktop layout */
@media (min-width: 1440px) {
  .gy-pcard-wrap {
    gap: 2.5rem;
  }

  .entry-title {
    font-size: 17px; /* Slightly larger on large screens */
  }
}

/* Country page specific layout - 2x3 layout with proper proportions */
.country-page .gy-pcard-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.country-page .gy-post-card {
  min-width: 320px;
  max-width: 450px;
  aspect-ratio: 4 / 3; /* GreatYOP-style rectangular aspect ratio */
}

.country-page .gyp-article-thumb {
  height: 45%; /* Further reduced height to give more space for title and content */
}

@media (min-width: 640px) {
  .country-page .gy-pcard-wrap {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .country-page .gy-post-card {
    min-width: auto;
    max-width: none;
    aspect-ratio: 4 / 3; /* GreatYOP-style rectangular aspect ratio */
  }

  .country-page .gyp-article-thumb {
    height: 55%; /* Increased height for better image display */
  }
}

@media (min-width: 1024px) {
  .country-page .gy-pcard-wrap {
    grid-template-columns: repeat(2, 1fr);
    gap: 2.5rem;
  }

  .country-page .gyp-article-thumb {
    height: 55%; /* Increased height for better image display */
  }
}

/* Status colors */
.gy-pcard-bottom .text-red-600 {
  color: #dc2626;
}

.gy-pcard-bottom .text-amber-600 {
  color: #d97706;
}

.gy-pcard-bottom .text-gray-600 {
  color: #4b5563;
}

/* Animation for card entrance */
.gy-post-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.gy-post-card .animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* GreatYOP Pagination Styles */
.gy-paginat-position {
  display: flex;
  justify-content: center;
  margin: 0.5rem 0 0.5rem 0;
  width: 100%;
  clear: both;
}

.navigation.pagination {
  display: flex;
  align-items: center;
}

.nav-links {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.page-numbers {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #374151;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.page-numbers:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
  color: #111827;
}

.page-numbers.current {
  background-color: #3c6c90;
  border-color: #3c6c90;
  color: #ffffff;
  font-weight: 600;
}

.page-numbers.current:hover {
  background-color: #2d5a7b;
  border-color: #2d5a7b;
}

.page-numbers.dots {
  border: none;
  background: none;
  color: #9ca3af;
  cursor: default;
  pointer-events: none;
}

.page-numbers.prev-page,
.page-numbers.next-page {
  font-weight: 600;
}

.page-numbers.prev-page:hover,
.page-numbers.next-page:hover {
  background-color: #3c6c90;
  border-color: #3c6c90;
  color: #ffffff;
}

/* FontAwesome caret icons */
.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

/* Screen reader only text */
.screen-reader-text,
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive pagination */
@media (max-width: 640px) {
  .page-numbers {
    min-width: 1.75rem;
    height: 1.75rem;
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
  }

  .nav-links {
    gap: 0.125rem;
  }
}
