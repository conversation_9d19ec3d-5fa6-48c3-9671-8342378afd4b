/* Opportunity card title scaling to avoid overflowing the image area and preserve layout */

/* Container limits title to two lines and scales font size for long titles */
.op-card-title {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  /* Base size matches scholarship card refined size */
  font-size: 16px;
  line-height: 1.2;
  font-weight: 700;
}

/* Dynamic size reductions based on title length (applied via extra classes) */
.op-card-title.op-title-medium { font-size: 15px; }
.op-card-title.op-title-small { font-size: 14px; }
.op-card-title.op-title-xsmall { font-size: 13px; }

/* Lightly reduce font-size when card width is smaller */
@media (max-width: 1023px) {
  .op-card-title { font-size: 15px; }
  .op-card-title.op-title-medium { font-size: 14.5px; }
  .op-card-title.op-title-small { font-size: 13.5px; }
}

@media (max-width: 639px) {
  .op-card-title { font-size: 14px; }
  .op-card-title.op-title-medium { font-size: 13.5px; }
  .op-card-title.op-title-small { font-size: 13px; }
}

/* Extra-small safety for very narrow screens */
@media (max-width: 380px) {
  .op-card-title { font-size: 13.5px; }
  .op-card-title.op-title-medium { font-size: 13px; }
  .op-card-title.op-title-small { font-size: 12.5px; }
}

/* Ensure the header block remains tight */
.op-entry-header { margin: 0 0 4px; }



/* Make opportunity cards identical to scholarships but with 60% image height */
.op-cards .gy-post-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.op-cards .gy-post-card article {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.op-cards .gyp-article-thumb {
  height: 60% !important;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

/* Content area takes exactly 40% and is split between header and bottom */
.op-cards .gyp-archive-post-header-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 0.5rem 1rem 0.25rem 1rem;
  min-height: 0; /* Allow shrinking */
}

.op-cards .gy-pcard-bottom {
  margin-top: auto;
  padding: 0 1rem 0.5rem 1rem;
  flex-shrink: 0;
}

.op-cards .entry-title {
  font-size: 15px;
  line-height: 1.1;
  margin: 0 0 2px 0;
  flex-shrink: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 33px; /* Strict height control: 15px * 1.1 * 2 lines */
}

/* Responsive adjustments maintain 60% image consistently */
@media (max-width: 639px) {
  .op-cards .gyp-article-thumb { height: 60% !important; }
  .op-cards .entry-title {
    font-size: 13px;
    line-height: 1.1;
    max-height: 29px; /* 13px * 1.1 * 2 lines */
  }
}

@media (min-width: 640px) and (max-width: 1023px) {
  .op-cards .gyp-article-thumb { height: 60% !important; }
  .op-cards .entry-title {
    font-size: 14px;
    line-height: 1.1;
    max-height: 31px; /* 14px * 1.1 * 2 lines */
  }
}

@media (min-width: 1024px) {
  .op-cards .gyp-article-thumb { height: 60% !important; }
  .op-cards .entry-title {
    font-size: 15px;
    line-height: 1.1;
    max-height: 33px; /* 15px * 1.1 * 2 lines */
  }
}

/* Full Width 3x2 Grid Layout for Opportunity Pages */
.full-width-grid.gy-pcard-wrap {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(1, 1fr);
}

/* Mobile: 1 column */
@media (min-width: 640px) {
  .full-width-grid.gy-pcard-wrap {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop: 3 columns for perfect 3x2 grid */
@media (min-width: 1024px) {
  .full-width-grid.gy-pcard-wrap {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Ensure cards in full-width grid maintain proper aspect ratio */
.full-width-grid .gy-post-card {
  height: 320px;
}

@media (min-width: 640px) {
  .full-width-grid .gy-post-card {
    height: 340px;
  }
}

@media (min-width: 1024px) {
  .full-width-grid .gy-post-card {
    height: 360px;
  }
}
