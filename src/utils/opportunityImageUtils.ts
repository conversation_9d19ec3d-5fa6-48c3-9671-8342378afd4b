/**
 * Opportunity Image Utilities
 * Handles image paths and fallbacks for opportunity cards
 */

export interface OpportunityImageConfig {
  type: 'internship' | 'training' | 'conference' | 'workshop' | 'competition';
  defaultImages: string[];
}

// Default images for each opportunity type
export const OPPORTUNITY_IMAGE_MAP: Record<string, string[]> = {
  internship: [
    '/uploads/opportunities/internship-1.jpg',
  ],
  training: [
    '/uploads/opportunities/training-1.jpg',
    '/uploads/opportunities/training-2.jpg',
  ],
  conference: [
    '/uploads/opportunities/conference-1.jpg',
  ],
  workshop: [
    '/uploads/opportunities/workshop-1.jpg',
  ],
  competition: [
    '/uploads/opportunities/competition-1.jpg',
  ],
};

// Default fallback image
export const DEFAULT_OPPORTUNITY_IMAGE = '/uploads/opportunities/default-opportunity.jpg';

/**
 * Get appropriate image for an opportunity
 * @param type - The opportunity type
 * @param thumbnail - The provided thumbnail (optional)
 * @param index - Index for rotating through available images
 * @returns Image URL
 */
export function getOpportunityImage(
  type: string,
  thumbnail?: string,
  index: number = 0
): string {
  // If thumbnail is provided and valid, use it
  if (thumbnail && thumbnail.trim() !== '' && !thumbnail.includes('default-opportunity')) {
    return thumbnail;
  }

  // Get images for this type
  const typeImages = OPPORTUNITY_IMAGE_MAP[type];
  if (typeImages && typeImages.length > 0) {
    // Rotate through available images based on index
    const imageIndex = index % typeImages.length;
    const apiBase = (process.env.REACT_APP_API_URL || 'http://localhost:5000').replace(/\/$/, '');
    return `${apiBase}${typeImages[imageIndex]}`;
  }

  // Fallback to default image
  const apiBase = (process.env.REACT_APP_API_URL || 'http://localhost:5000').replace(/\/$/, '');
  return `${apiBase}${DEFAULT_OPPORTUNITY_IMAGE}`;
}

/**
 * Get image alt text for opportunity
 * @param type - The opportunity type
 * @param title - The opportunity title
 * @returns Alt text for the image
 */
export function getOpportunityImageAlt(type: string, title: string): string {
  const typeNames: Record<string, string> = {
    internship: 'Stage',
    training: 'Formation',
    conference: 'Conférence',
    workshop: 'Atelier',
    competition: 'Concours',
  };

  const typeName = typeNames[type] || 'Opportunité';
  return `${typeName}: ${title}`;
}

/**
 * Preload opportunity images for better performance
 */
export function preloadOpportunityImages(): void {
  const allImages = Object.values(OPPORTUNITY_IMAGE_MAP).flat();
  allImages.push(DEFAULT_OPPORTUNITY_IMAGE);

  allImages.forEach(imagePath => {
    const img = new Image();
    img.src = imagePath;
  });
}
