/**
 * Test Admin Login Script
 * 
 * This script tests the admin login functionality to diagnose login issues
 */

const axios = require('axios');

async function testAdminLogin() {
  console.log('🔐 Testing Admin Login...\n');
  
  const credentials = {
    email: '<EMAIL>',
    password: 'admin123'
  };
  
  console.log(`📧 Email: ${credentials.email}`);
  console.log(`🔑 Password: ${credentials.password}`);
  console.log('');
  
  try {
    console.log('🚀 Attempting login...');
    
    const response = await axios.post('http://localhost:5000/api/auth/admin/login', credentials, {
      timeout: 10000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MaBourse-Test-Client/1.0'
      }
    });
    
    console.log('✅ Login successful!');
    console.log(`📊 Status: ${response.status}`);
    console.log(`📝 Message: ${response.data.message}`);
    
    if (response.data.data) {
      const admin = response.data.data;
      console.log('\n👤 Admin Details:');
      console.log(`   Name: ${admin.name}`);
      console.log(`   Email: ${admin.email}`);
      console.log(`   Role: ${admin.role}`);
      console.log(`   Main Admin: ${admin.isMainAdmin}`);
    }
    
    // Check cookies
    if (response.headers['set-cookie']) {
      console.log('\n🍪 Cookies set:');
      response.headers['set-cookie'].forEach(cookie => {
        console.log(`   ${cookie.split(';')[0]}`);
      });
    }
    
    return true;
    
  } catch (error) {
    console.log('❌ Login failed!');
    
    if (error.code === 'ECONNABORTED') {
      console.log('⏰ Request timed out - server may be unresponsive');
    } else if (error.response) {
      console.log(`📊 Status: ${error.response.status}`);
      console.log(`📝 Message: ${error.response.data?.message || 'Unknown error'}`);
      
      if (error.response.status === 401) {
        console.log('🔒 Authentication failed - check credentials');
      } else if (error.response.status === 429) {
        console.log('🚫 Rate limited - too many attempts');
      } else if (error.response.status === 500) {
        console.log('💥 Server error - check backend logs');
      }
    } else if (error.request) {
      console.log('🌐 No response from server - check if backend is running');
    } else {
      console.log(`❓ Unknown error: ${error.message}`);
    }
    
    return false;
  }
}

async function testDatabaseConnection() {
  console.log('🗄️  Testing Database Connection...\n');
  
  try {
    const response = await axios.get('http://localhost:5000/api/test-db', {
      timeout: 5000
    });
    
    console.log('✅ Database connection working');
    console.log(`📊 Admins: ${response.data.data.adminCount}`);
    console.log(`👥 Users: ${response.data.data.userCount}`);
    console.log(`🎓 Scholarships: ${response.data.data.scholarshipCount}`);
    
    return true;
  } catch (error) {
    console.log('❌ Database connection failed');
    console.log(`Error: ${error.message}`);
    return false;
  }
}

async function testServerHealth() {
  console.log('🏥 Testing Server Health...\n');
  
  try {
    const response = await axios.get('http://localhost:5000/api/health', {
      timeout: 5000
    });
    
    console.log('✅ Server is healthy');
    console.log(`📊 Status: ${response.data.status}`);
    console.log(`🌍 Environment: ${response.data.environment}`);
    console.log(`📅 Timestamp: ${response.data.timestamp}`);
    
    return true;
  } catch (error) {
    console.log('❌ Server health check failed');
    console.log(`Error: ${error.message}`);
    return false;
  }
}

async function runDiagnostics() {
  console.log('🔍 MaBourse Admin Login Diagnostics\n');
  console.log('=' .repeat(50));
  
  // Test server health
  const serverHealthy = await testServerHealth();
  console.log('');
  
  // Test database connection
  const dbConnected = await testDatabaseConnection();
  console.log('');
  
  // Test admin login
  const loginSuccessful = await testAdminLogin();
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 DIAGNOSTIC RESULTS');
  console.log('=' .repeat(50));
  
  console.log(`🏥 Server Health: ${serverHealthy ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🗄️  Database: ${dbConnected ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`🔐 Admin Login: ${loginSuccessful ? '✅ PASS' : '❌ FAIL'}`);
  
  if (!loginSuccessful) {
    console.log('\n🔧 TROUBLESHOOTING STEPS:');
    console.log('1. Verify admin credentials in backend/.env.development');
    console.log('2. Check if admin user exists in database');
    console.log('3. Review backend logs for authentication errors');
    console.log('4. Ensure rate limiting is not blocking requests');
    console.log('5. Check if authentication controller is working properly');
  } else {
    console.log('\n🎉 All systems operational! Admin login is working correctly.');
  }
  
  return loginSuccessful;
}

// Run diagnostics
if (require.main === module) {
  runDiagnostics()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Diagnostic failed:', error);
      process.exit(1);
    });
}

module.exports = { testAdminLogin, runDiagnostics };
