/**
 * Admin Portal API Test Script
 * 
 * Tests all admin portal API endpoints to ensure they're working correctly
 * with proper authentication and data flow.
 */

const axios = require('axios');

const BACKEND_URL = 'http://localhost:5000';

/**
 * Test admin authentication and endpoints
 */
async function testAdminPortal() {
  console.log('🔐 Testing Admin Portal APIs...\n');
  
  // Create axios instance with credentials
  const api = axios.create({
    baseURL: `${BACKEND_URL}/api`,
    timeout: 10000,
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  const tests = [
    {
      name: 'Admin Stats',
      method: 'GET',
      url: '/admin/stats',
      description: 'Get dashboard statistics'
    },
    {
      name: 'Current Admin',
      method: 'GET', 
      url: '/admin/me',
      description: 'Get current admin profile'
    },
    {
      name: 'All Admins',
      method: 'GET',
      url: '/admin/all',
      description: 'Get all admin accounts'
    },
    {
      name: 'Newsletter Subscribers',
      method: 'GET',
      url: '/newsletter/subscribers',
      description: 'Get newsletter subscribers'
    },
    {
      name: 'Messages',
      method: 'GET',
      url: '/messages',
      description: 'Get contact messages'
    },
    {
      name: 'Analytics',
      method: 'GET',
      url: '/admin/analytics',
      description: 'Get analytics data'
    }
  ];
  
  let passed = 0;
  let failed = 0;
  let authRequired = 0;
  
  for (const test of tests) {
    try {
      console.log(`🧪 Testing ${test.name}...`);
      
      const response = await api({
        method: test.method,
        url: test.url
      });
      
      if (response.status === 200) {
        console.log(`✅ ${test.name} - PASSED`);
        
        // Log sample data structure
        if (response.data) {
          if (response.data.success) {
            console.log(`   📊 Success: ${response.data.success}`);
            if (response.data.data) {
              if (Array.isArray(response.data.data)) {
                console.log(`   📋 Data: Array with ${response.data.data.length} items`);
              } else if (typeof response.data.data === 'object') {
                console.log(`   📋 Data: Object with keys: ${Object.keys(response.data.data).join(', ')}`);
              }
            }
          }
        }
        passed++;
      } else {
        console.log(`❌ ${test.name} - FAILED (Status: ${response.status})`);
        failed++;
      }
      
    } catch (error) {
      if (error.response?.status === 401) {
        console.log(`🔒 ${test.name} - REQUIRES AUTHENTICATION`);
        authRequired++;
      } else if (error.response?.status === 403) {
        console.log(`🚫 ${test.name} - FORBIDDEN (Insufficient privileges)`);
        failed++;
      } else {
        console.log(`❌ ${test.name} - ERROR: ${error.message}`);
        failed++;
      }
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Test public endpoints that should work without auth
  console.log('🌐 Testing Public Endpoints...');
  
  const publicTests = [
    { name: 'Health Check', url: '/health' },
    { name: 'Scholarships', url: '/scholarships?limit=3' },
    { name: 'Opportunities', url: '/opportunities?limit=3' },
    { name: 'Countries', url: '/countries' }
  ];
  
  for (const test of publicTests) {
    try {
      const response = await api.get(test.url);
      if (response.status === 200) {
        console.log(`✅ ${test.name} - PASSED`);
        passed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} - ERROR: ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n📊 Admin Portal Test Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`🔒 Auth Required: ${authRequired}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed + authRequired)) * 100).toFixed(1)}%`);
  
  if (authRequired > 0) {
    console.log('\n🔐 Authentication Required Tests:');
    console.log('These endpoints require admin authentication, which is expected behavior.');
    console.log('To test authenticated endpoints, you would need to:');
    console.log('1. Login through the admin portal');
    console.log('2. Use the session cookies for subsequent requests');
  }
  
  if (failed === 0) {
    console.log('\n🎉 All accessible endpoints are working correctly!');
    console.log('🚀 Admin portal API integration is ready for production use!');
  } else {
    console.log('\n⚠️  Some endpoints failed. Please check the errors above.');
  }
  
  return failed === 0;
}

/**
 * Test admin login flow
 */
async function testAdminLogin() {
  console.log('\n🔐 Testing Admin Login Flow...');
  
  const api = axios.create({
    baseURL: `${BACKEND_URL}/api`,
    timeout: 10000,
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
    },
  });
  
  try {
    // Test login endpoint exists
    const response = await api.post('/auth/admin/login', {
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    
    console.log('❌ Login test - Unexpected success with wrong credentials');
    return false;
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Login endpoint - WORKING (correctly rejected invalid credentials)');
      return true;
    } else if (error.response?.status === 429) {
      console.log('✅ Login endpoint - WORKING (rate limiting active)');
      return true;
    } else {
      console.log(`❌ Login endpoint - ERROR: ${error.message}`);
      return false;
    }
  }
}

/**
 * Main test runner
 */
async function runAdminPortalTests() {
  console.log('🚀 Starting Admin Portal Tests...\n');
  console.log('=' .repeat(60));
  
  const loginTest = await testAdminLogin();
  const apiTest = await testAdminPortal();
  
  console.log('\n' + '=' .repeat(60));
  console.log('📊 FINAL ADMIN PORTAL TEST RESULTS');
  console.log('=' .repeat(60));
  
  const overallSuccess = loginTest && apiTest;
  
  console.log(`🔐 Login Flow: ${loginTest ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`📡 API Endpoints: ${apiTest ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (overallSuccess) {
    console.log('\n🎉 ADMIN PORTAL TESTS SUCCESSFUL!');
    console.log('🚀 Admin portal is ready for production use!');
  } else {
    console.log('\n⚠️  ADMIN PORTAL TESTS INCOMPLETE');
    console.log('🔧 Please address the failed tests.');
  }
  
  return overallSuccess;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAdminPortalTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runAdminPortalTests };
