/**
 * API Integration Test Script
 * 
 * This script tests the API integration between frontend and backend
 * to ensure all endpoints are working correctly.
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test configuration
const tests = [
  {
    name: 'Health Check',
    method: 'GET',
    url: `${API_BASE_URL}/health`,
    expectedStatus: 200
  },
  {
    name: 'Database Connection Test',
    method: 'GET',
    url: `${API_BASE_URL}/test-db`,
    expectedStatus: 200
  },
  {
    name: 'Scholarships List',
    method: 'GET',
    url: `${API_BASE_URL}/scholarships?limit=5`,
    expectedStatus: 200
  },
  {
    name: 'Opportunities List',
    method: 'GET',
    url: `${API_BASE_URL}/opportunities?limit=5`,
    expectedStatus: 200
  },
  {
    name: 'Countries List',
    method: 'GET',
    url: `${API_BASE_URL}/countries`,
    expectedStatus: 200
  },
  {
    name: 'Guides List',
    method: 'GET',
    url: `${API_BASE_URL}/guides`,
    expectedStatus: 200
  }
];

/**
 * Run a single test
 */
async function runTest(test) {
  try {
    console.log(`🧪 Testing: ${test.name}`);
    
    const startTime = Date.now();
    const response = await axios({
      method: test.method,
      url: test.url,
      timeout: 10000
    });
    const duration = Date.now() - startTime;
    
    if (response.status === test.expectedStatus) {
      console.log(`✅ ${test.name} - PASSED (${duration}ms)`);
      
      // Log some response details
      if (response.data) {
        if (response.data.data && Array.isArray(response.data.data)) {
          console.log(`   📊 Returned ${response.data.data.length} items`);
        } else if (response.data.data && response.data.data.opportunities) {
          console.log(`   📊 Returned ${response.data.data.opportunities.length} opportunities`);
        }
        
        if (response.data.pagination) {
          console.log(`   📄 Pagination: ${response.data.pagination.page}/${response.data.pagination.totalPages} (${response.data.pagination.total} total)`);
        }
      }
      
      return { success: true, duration, response: response.data };
    } else {
      console.log(`❌ ${test.name} - FAILED (Expected ${test.expectedStatus}, got ${response.status})`);
      return { success: false, duration, error: `Status mismatch` };
    }
    
  } catch (error) {
    console.log(`❌ ${test.name} - ERROR: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test image serving
 */
async function testImageServing() {
  console.log('\n🖼️  Testing Image Serving...');
  
  try {
    // Get a scholarship with an image
    const scholarshipsResponse = await axios.get(`${API_BASE_URL}/scholarships?limit=1`);
    
    if (scholarshipsResponse.data.data && scholarshipsResponse.data.data.length > 0) {
      const scholarship = scholarshipsResponse.data.data[0];
      
      if (scholarship.thumbnail) {
        const imageUrl = `http://localhost:5000${scholarship.thumbnail}`;
        console.log(`🧪 Testing image: ${imageUrl}`);
        
        const imageResponse = await axios.head(imageUrl);
        
        if (imageResponse.status === 200) {
          console.log(`✅ Image serving - PASSED`);
          console.log(`   📏 Content-Type: ${imageResponse.headers['content-type']}`);
          console.log(`   📦 Content-Length: ${imageResponse.headers['content-length']} bytes`);
          
          // Test thumbnail
          const filename = scholarship.thumbnail.split('/').pop().replace(/\.[^/.]+$/, '');
          const thumbnailUrl = `http://localhost:5000/uploads/thumbnails/scholarships/${filename}_card.webp`;
          
          try {
            const thumbnailResponse = await axios.head(thumbnailUrl);
            if (thumbnailResponse.status === 200) {
              console.log(`✅ Thumbnail serving - PASSED`);
              console.log(`   📏 Thumbnail Content-Type: ${thumbnailResponse.headers['content-type']}`);
            }
          } catch (thumbError) {
            console.log(`⚠️  Thumbnail serving - WARNING: ${thumbError.message}`);
          }
          
          return true;
        }
      } else {
        console.log(`⚠️  No thumbnail found in scholarship data`);
      }
    }
    
  } catch (error) {
    console.log(`❌ Image serving - ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Test CORS configuration
 */
async function testCORS() {
  console.log('\n🌐 Testing CORS Configuration...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health`, {
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });
    
    console.log(`✅ CORS - PASSED`);
    console.log(`   🔗 Access-Control-Allow-Credentials: ${response.headers['access-control-allow-credentials']}`);
    
    return true;
  } catch (error) {
    console.log(`❌ CORS - ERROR: ${error.message}`);
    return false;
  }
}

/**
 * Main test runner
 */
async function runAllTests() {
  console.log('🚀 Starting API Integration Tests...\n');
  
  const results = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
  };
  
  // Run basic API tests
  for (const test of tests) {
    const result = await runTest(test);
    results.total++;
    
    if (result.success) {
      results.passed++;
    } else {
      results.failed++;
    }
    
    results.details.push({
      name: test.name,
      success: result.success,
      duration: result.duration,
      error: result.error
    });
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // Test image serving
  await testImageServing();
  
  // Test CORS
  await testCORS();
  
  // Print summary
  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${((results.passed / results.total) * 100).toFixed(1)}%`);
  
  if (results.failed > 0) {
    console.log('\n❌ Failed Tests:');
    results.details
      .filter(detail => !detail.success)
      .forEach(detail => {
        console.log(`   - ${detail.name}: ${detail.error}`);
      });
  }
  
  console.log('\n🎉 API Integration Tests Completed!');
  
  return results.failed === 0;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runAllTests };
