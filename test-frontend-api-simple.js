/**
 * Simple Frontend API Test
 * 
 * Tests if the frontend can successfully fetch data from the backend
 */

const axios = require('axios');

async function testFrontendAPI() {
  console.log('🧪 Testing Frontend API Integration...\n');
  
  const tests = [
    {
      name: 'Scholarships API',
      url: 'http://localhost:5000/api/scholarships?limit=3',
      checkData: (data) => data.data && Array.isArray(data.data) && data.data.length > 0
    },
    {
      name: 'Opportunities API',
      url: 'http://localhost:5000/api/opportunities?limit=3',
      checkData: (data) => data.data && Array.isArray(data.data)
    },
    {
      name: 'Countries API',
      url: 'http://localhost:5000/api/countries',
      checkData: (data) => data.data && Array.isArray(data.data) && data.data.length > 0
    },
    {
      name: 'Guides API',
      url: 'http://localhost:5000/api/guides?limit=3',
      checkData: (data) => data.data && Array.isArray(data.data) && data.data.length > 0
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log(`🔍 Testing ${test.name}...`);
      
      const response = await axios.get(test.url, {
        timeout: 10000,
        headers: {
          'Origin': 'http://localhost:3000'
        }
      });
      
      if (response.status === 200 && test.checkData(response.data)) {
        console.log(`✅ ${test.name} - PASSED`);
        
        // Log sample data
        if (response.data.data && response.data.data[0]) {
          const sample = response.data.data[0];
          console.log(`   📊 Sample: ${sample.title || sample.country || sample.name || 'N/A'}`);
          if (sample.thumbnail) {
            console.log(`   🖼️  Image: ${sample.thumbnail}`);
          }
        } else if (response.data.data && response.data.data[0] && response.data.data[0].title) {
          const sample = response.data.data[0];
          console.log(`   📊 Sample: ${sample.title}`);
        }
        
        passed++;
      } else {
        console.log(`❌ ${test.name} - FAILED (Invalid data structure)`);
        console.log(`   Response:`, JSON.stringify(response.data, null, 2).substring(0, 200) + '...');
        failed++;
      }
      
    } catch (error) {
      console.log(`❌ ${test.name} - ERROR: ${error.message}`);
      failed++;
    }
    
    console.log(''); // Empty line for readability
  }
  
  // Test image serving
  console.log('🖼️  Testing Image Serving...');
  try {
    const scholarshipsResponse = await axios.get('http://localhost:5000/api/scholarships?limit=1');
    
    if (scholarshipsResponse.data.data && scholarshipsResponse.data.data[0] && scholarshipsResponse.data.data[0].thumbnail) {
      const imageUrl = `http://localhost:5000${scholarshipsResponse.data.data[0].thumbnail}`;
      const imageResponse = await axios.head(imageUrl);
      
      if (imageResponse.status === 200) {
        console.log('✅ Image Serving - PASSED');
        console.log(`   📸 Image URL: ${imageUrl}`);
        console.log(`   📏 Content-Type: ${imageResponse.headers['content-type']}`);
        passed++;
      } else {
        console.log('❌ Image Serving - FAILED');
        failed++;
      }
    } else {
      console.log('⚠️  Image Serving - SKIPPED (No images in data)');
    }
  } catch (error) {
    console.log(`❌ Image Serving - ERROR: ${error.message}`);
    failed++;
  }
  
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Frontend should be able to load data correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the errors above.');
  }
  
  return failed === 0;
}

// Run the test
testFrontendAPI()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Test failed:', error);
    process.exit(1);
  });
