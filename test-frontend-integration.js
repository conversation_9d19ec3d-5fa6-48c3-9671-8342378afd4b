/**
 * Frontend Integration Test Script
 * 
 * This script tests the frontend integration by checking if the React app
 * can properly fetch and display data from the backend API.
 */

const puppeteer = require('puppeteer');
const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:5000';

/**
 * Test if frontend and backend are running
 */
async function testServersRunning() {
  console.log('🔍 Checking if servers are running...');
  
  try {
    // Test backend
    const backendResponse = await axios.get(`${BACKEND_URL}/api/health`, { timeout: 5000 });
    console.log('✅ Backend is running');
    
    // Test frontend
    const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
    console.log('✅ Frontend is running');
    
    return true;
  } catch (error) {
    console.error('❌ Server check failed:', error.message);
    return false;
  }
}

/**
 * Test frontend data loading with browser automation
 */
async function testFrontendDataLoading() {
  console.log('\n🌐 Testing frontend data loading...');
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set up console logging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('🔴 Frontend Error:', msg.text());
      }
    });
    
    // Set up network request monitoring
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        requests.push({
          url: request.url(),
          method: request.method()
        });
      }
    });
    
    // Navigate to homepage
    console.log('📄 Loading homepage...');
    await page.goto(FRONTEND_URL, { waitUntil: 'networkidle2', timeout: 30000 });
    
    // Wait for React to load
    await page.waitForTimeout(3000);
    
    // Check if scholarships are loaded
    console.log('🔍 Checking for scholarship cards...');
    const scholarshipCards = await page.$$('[data-testid="scholarship-card"], .scholarship-card, .card');
    console.log(`📊 Found ${scholarshipCards.length} scholarship cards`);
    
    // Check if images are loading
    console.log('🖼️  Checking image loading...');
    const images = await page.$$('img');
    let loadedImages = 0;
    
    for (const img of images) {
      const src = await img.evaluate(el => el.src);
      if (src && !src.includes('data:')) {
        try {
          const response = await axios.head(src, { timeout: 5000 });
          if (response.status === 200) {
            loadedImages++;
          }
        } catch (error) {
          console.log(`⚠️  Image failed to load: ${src}`);
        }
      }
    }
    
    console.log(`📸 ${loadedImages}/${images.length} images loaded successfully`);
    
    // Check API requests made by frontend
    console.log('\n📡 API requests made by frontend:');
    requests.forEach(req => {
      console.log(`   ${req.method} ${req.url}`);
    });
    
    // Test navigation to scholarships page
    console.log('\n🔗 Testing navigation...');
    try {
      await page.goto(`${FRONTEND_URL}/scholarships`, { waitUntil: 'networkidle2', timeout: 15000 });
      console.log('✅ Scholarships page loaded');
    } catch (error) {
      console.log('⚠️  Scholarships page navigation failed:', error.message);
    }
    
    // Test navigation to opportunities page
    try {
      await page.goto(`${FRONTEND_URL}/opportunities`, { waitUntil: 'networkidle2', timeout: 15000 });
      console.log('✅ Opportunities page loaded');
    } catch (error) {
      console.log('⚠️  Opportunities page navigation failed:', error.message);
    }
    
    return {
      success: true,
      scholarshipCards: scholarshipCards.length,
      loadedImages,
      totalImages: images.length,
      apiRequests: requests.length
    };
    
  } catch (error) {
    console.error('❌ Frontend test failed:', error.message);
    return { success: false, error: error.message };
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * Test specific frontend components
 */
async function testFrontendComponents() {
  console.log('\n🧩 Testing frontend components...');
  
  const tests = [
    {
      name: 'Homepage loads',
      url: FRONTEND_URL,
      expectedElements: ['header', 'main', 'footer']
    },
    {
      name: 'Scholarships page loads',
      url: `${FRONTEND_URL}/scholarships`,
      expectedElements: ['.scholarship-card', '.pagination']
    },
    {
      name: 'Opportunities page loads',
      url: `${FRONTEND_URL}/opportunities`,
      expectedElements: ['.opportunity-card']
    }
  ];
  
  let browser;
  try {
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const results = [];
    
    for (const test of tests) {
      try {
        const page = await browser.newPage();
        await page.goto(test.url, { waitUntil: 'networkidle2', timeout: 15000 });
        
        let elementsFound = 0;
        for (const selector of test.expectedElements) {
          const elements = await page.$$(selector);
          if (elements.length > 0) {
            elementsFound++;
          }
        }
        
        const success = elementsFound > 0;
        console.log(`${success ? '✅' : '❌'} ${test.name} - ${elementsFound}/${test.expectedElements.length} elements found`);
        
        results.push({
          name: test.name,
          success,
          elementsFound,
          totalExpected: test.expectedElements.length
        });
        
        await page.close();
        
      } catch (error) {
        console.log(`❌ ${test.name} - ERROR: ${error.message}`);
        results.push({
          name: test.name,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
    
  } catch (error) {
    console.error('❌ Component testing failed:', error.message);
    return [];
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * Main test runner
 */
async function runFrontendTests() {
  console.log('🚀 Starting Frontend Integration Tests...\n');
  
  // Check if servers are running
  const serversRunning = await testServersRunning();
  if (!serversRunning) {
    console.log('❌ Cannot proceed - servers are not running');
    return false;
  }
  
  // Test data loading
  const dataLoadingResult = await testFrontendDataLoading();
  
  // Test components
  const componentResults = await testFrontendComponents();
  
  // Print summary
  console.log('\n📊 Frontend Integration Test Summary:');
  console.log(`🌐 Data Loading: ${dataLoadingResult.success ? 'PASSED' : 'FAILED'}`);
  
  if (dataLoadingResult.success) {
    console.log(`   📊 Scholarship Cards: ${dataLoadingResult.scholarshipCards}`);
    console.log(`   🖼️  Images: ${dataLoadingResult.loadedImages}/${dataLoadingResult.totalImages}`);
    console.log(`   📡 API Requests: ${dataLoadingResult.apiRequests}`);
  }
  
  console.log(`🧩 Component Tests: ${componentResults.filter(r => r.success).length}/${componentResults.length} passed`);
  
  const overallSuccess = dataLoadingResult.success && componentResults.every(r => r.success);
  console.log(`\n🎯 Overall Result: ${overallSuccess ? 'SUCCESS' : 'NEEDS ATTENTION'}`);
  
  return overallSuccess;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runFrontendTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner failed:', error);
      process.exit(1);
    });
}

module.exports = { runFrontendTests };
